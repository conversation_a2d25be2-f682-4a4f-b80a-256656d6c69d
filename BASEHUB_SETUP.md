# Basehub Setup Guide for ALIAS Showcase

## Overview
The ALIAS Showcase is now configured to use Basehub as a content management system. This allows you to manage content for multiple business profiles (ALIAS, ARA Property Services, etc.) from a central location.

## What's Been Set Up

### 1. **Basehub Integration**
- Installed Basehub package
- Created configuration schema in `basehub.config.ts`
- Set up API client in `src/lib/basehub-client.js`
- Created React hooks in `src/hooks/useBasehub.js`

### 2. **Profile System**
- **ALIAS** - Your main company profile
- **ARA Property Services** - Example client alias profile
- Profile switcher component to toggle between businesses

### 3. **Content Manager**
- New page at `/content-manager`
- Visual editor for all content sections
- Changes propagate to all showcase websites

### 4. **Content Sections**
Each profile can have:
- Hero Section (headline, subheadline, CTA)
- About Section (philosophy, mission, values)
- Services (list of offerings)
- Portfolio Projects (case studies)
- Testimonials (client reviews)
- Contact Information

## Next Steps to Activate Basehub

### 1. Create Basehub Account
1. Go to [basehub.com](https://basehub.com)
2. Sign up for an account
3. Create a new repository

### 2. Configure Your Repository
1. In Basehub dashboard, create collections matching our schema:
   - Business Profiles
   - Hero Sections
   - About Sections
   - Services
   - Portfolio Projects
   - Testimonials
   - Team Members
   - Contact Info
   - Global Settings

### 3. Get Your API Token
1. In Basehub settings, generate an API token
2. Copy the token and your repository URL

### 4. Update Environment Variables
Edit `.env.local`:
```env
BASEHUB_TOKEN=your_actual_token_here
NEXT_PUBLIC_BASEHUB_URL=https://api.basehub.com/your-repo
```

### 5. Seed Initial Data
You can import the content from:
- `/src/data/alias-content.md` - ALIAS company content
- `/src/data/ara-content.md` - ARA Property Services content

## How It Works

### For End Users
1. Visit the ALIAS Showcase
2. Use the profile switcher (top right) to switch between ALIAS and ARA
3. All websites in the showcase will update to show the selected profile's content

### For Content Editors
1. Go to `/content-manager`
2. Select a content section from the sidebar
3. Click any field to edit
4. Save changes - they'll sync to Basehub
5. All showcase websites automatically update

### For Developers
The system uses:
- Basehub for content storage
- React hooks for data fetching
- Local storage for profile persistence
- Dynamic content injection into showcase sites

## Benefits

1. **Centralized Content Management**
   - Edit once, update everywhere
   - No need to modify code for content changes

2. **Multiple Business Profiles**
   - Show different versions of sites for different clients
   - Perfect for demonstrating "digital alias" concept

3. **Version Control**
   - Basehub tracks all content changes
   - Easy rollback if needed

4. **API-First**
   - Content available via API for other applications
   - Can integrate with other systems

## Extending the System

To add a new business profile:
1. Create profile in Basehub
2. Add content for each section
3. Profile automatically appears in switcher

To add to a showcase website:
1. Import content hooks in the website
2. Replace static content with dynamic data
3. Website automatically supports all profiles

## Current Status

✅ Basehub integration code complete
✅ Profile switcher implemented
✅ Content manager UI created
✅ Sample content for ALIAS and ARA
⏳ Awaiting Basehub account setup
⏳ Ready for content synchronization

Once you set up your Basehub account and add the API token, the entire system will be fully operational!