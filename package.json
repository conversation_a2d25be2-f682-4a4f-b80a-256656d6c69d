{"name": "codegrid-showcase", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.4", "@studio-freight/lenis": "^1.0.42", "basehub": "^9.0.19", "clsx": "^2.1.1", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "gsap": "^3.13.0", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "next": "15.3.4", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-live": "^4.1.8", "split-type": "^0.3.4", "three": "^0.160.1", "zustand": "^4.5.7"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}