{"name": "codegrid-showcase", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "turbo run build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "collect-sites": "node scripts/collect-sites.js", "export-sites": "node scripts/export-sites.js", "export-siblings": "npm run collect-sites && npm run export-sites", "analyze": "ANALYZE=true npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install"}, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.4", "@studio-freight/lenis": "^1.0.42", "basehub": "^9.0.19", "clsx": "^2.1.1", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "gsap": "^3.13.0", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "next": "15.3.4", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-live": "^4.1.8", "split-type": "^0.3.4", "three": "^0.160.1", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/bundle-analyzer": "^15.3.4", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "typescript": "^5"}}