# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

CodeGRID Showcase is a comprehensive multi-website showcase system that demonstrates ALIAS's web solutions with dynamic content management via Basehub CMS. The system allows switching between different business profiles (ALIAS, ARA Property Services) with content dynamically updating across all showcase websites running on ports 6661-6683.

## Common Development Commands

### Install Dependencies
```bash
# Use bun package manager
bun install
```

### Development
```bash
# Start the showcase (development mode with Turbopack)
bun dev

# Build for production
bun build

# Start production server
bun start

# Lint code
bun lint
```

### Run All Showcase Websites
```bash
# From parent directory
cd ..
./start-simple.sh

# To stop all websites
./stop-all.sh
```

## Project Architecture

### Technology Stack
- **Next.js 15.3.4** with React 19 and Turbopack
- **Basehub CMS** for content management
- **Tailwind CSS v4** for styling
- **GSAP** for animations
- **Three.js** with React Three Fiber for 3D graphics
- **Framer Motion** for component animations
- **Zustand** for state management
- **Lenis** for smooth scrolling
- **SplitType** for text animations
- **Prism** and React Live for code playground

### Directory Structure
```
codegrid-showcase/
├── app/                        # Next.js app router
│   ├── page.js                # Main showcase listing
│   ├── content-manager/       # Visual content editor
│   ├── preview/[id]/         # Device preview (iPhone/iPad/Desktop)
│   ├── playground/           # Component playground
│   ├── my-blocks/           # Custom component blocks
│   └── api/                 # API routes for export/themes
├── src/
│   ├── components/          # React components
│   │   ├── ProfileSwitcher  # Business profile switcher
│   │   ├── showcase/        # Showcase UI components
│   │   ├── editor/          # Content editor components
│   │   └── preview/         # Device preview components
│   ├── data/               # Content and configuration
│   │   ├── websites.js     # Showcase websites configuration
│   │   ├── content-manager.js # Content sections mapping
│   │   └── themes/         # UI themes
│   ├── hooks/              # Custom React hooks
│   │   └── useBasehub.js   # Basehub integration hook
│   └── lib/                # Utilities
│       └── basehub-client.js # Basehub API client
├── basehub.config.ts       # Basehub schema configuration
└── .basehub/              # Auto-generated Basehub types
```

## Key Features

### Multi-Profile System
The showcase supports multiple business profiles that dynamically change content across all websites:
- **ALIAS** - Main company profile
- **ARA Property Services** - Example client alias
- Profile state persists in localStorage
- Profile switcher component in top navigation

### Showcase Websites
All websites run on sequential ports and are configured in `src/data/websites.js`:
- Format Archive (6661) - E-commerce platform
- Isochrome (6662) - Creative agency
- Algora (6663) - Tech showcase
- Wu-Wei (6664) - Experimental lab
- Nico Palmer (6665) - Consulting portfolio
- Otis Valen (6666) - Digital gallery
- Alex Finley (6667) - Creative studio
- Plus 17 additional component demos and vanilla JS projects

### Content Management
The `/content-manager` page provides visual editing for all Basehub collections:
- Hero Sections
- About Content
- Services
- Portfolio Projects
- Testimonials
- Team Members
- Contact Information

### Device Previews
The `/preview/[id]` routes show websites in device mockups:
- iPhone 16 Pro Max (430x932)
- iPad Pro 12.9" (1024x1366)
- Desktop view

## Basehub Integration

### Environment Variables
```env
BASEHUB_TOKEN=your_basehub_token
NEXT_PUBLIC_BASEHUB_URL=https://api.basehub.com/your-repo
```

### Schema Configuration
The Basehub schema is defined in `basehub.config.ts` with collections for:
- businessProfiles - Company profiles
- heroSections - Landing page heroes
- aboutSections - Company information
- services - Service offerings
- portfolioProjects - Case studies
- testimonials - Client reviews
- teamMembers - Staff profiles
- contactInfo - Contact details
- globalSettings - System configuration

### Content Flow
1. Content fetched via `useBasehub` hook
2. Filtered by current profile ID
3. Cached in component state
4. Updates propagate to all components

## Development Patterns

### Component Structure
- Functional components with hooks
- CSS modules or Tailwind classes
- GSAP animations via useEffect
- Responsive design patterns

### State Management
- Zustand for global state
- React hooks for local state
- localStorage for persistence
- Basehub for content state

### Animation Patterns
- GSAP ScrollTrigger for scroll animations
- Framer Motion for component transitions
- Three.js for 3D graphics
- Lenis for smooth scrolling

### Performance Considerations
- Lazy loading for images
- Code splitting by route
- Turbopack for fast development
- Optimized production builds

## Adding New Features

### New Showcase Website
1. Add entry to `src/data/websites.js`
2. Assign next sequential port
3. Update `../start-simple.sh` script
4. Map content in `src/data/content-manager.js`

### New Business Profile
1. Create profile in Basehub
2. Add content for each section
3. Profile appears automatically in switcher

### New Content Section
1. Add collection to `basehub.config.ts`
2. Create editor component in `src/components/editor/`
3. Add to content manager sidebar
4. Implement data fetching with `useBasehub`

## Testing Approach
The project currently doesn't have a test setup. When implementing tests:
- Consider Jest + React Testing Library
- Test Basehub hooks with mock data
- Test animation cleanup in components
- Verify profile switching logic