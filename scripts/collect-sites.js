#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const WEBSITES_DIR = path.resolve(__dirname, '../../');
const OUTPUT_DIR = path.resolve(__dirname, '../src/generated');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'sites.json');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Function to extract metadata from package.json
function extractMetadata(projectPath) {
  const packageJsonPath = path.join(projectPath, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    return null;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Extract basic info
    const name = packageJson.name || path.basename(projectPath);
    const description = packageJson.description || 'No description available';
    const version = packageJson.version || '1.0.0';
    
    // Determine project type and framework
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    let framework = 'vanilla';
    let port = 3000;
    
    if (dependencies.next) {
      framework = 'nextjs';
      port = 3000;
    } else if (dependencies.react) {
      framework = 'react';
      port = 3000;
    } else if (dependencies.vue) {
      framework = 'vue';
      port = 8080;
    } else if (dependencies.vite) {
      framework = 'vite';
      port = 5173;
    }
    
    // Generate slug from directory name
    const slug = path.basename(projectPath).toLowerCase().replace(/[^a-z0-9-]/g, '-');
    
    return {
      id: slug,
      name: name.replace(/^(cg-|codegrid-)/, '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      slug,
      description,
      version,
      framework,
      port,
      path: projectPath,
      screenshot: `/screenshots/${slug}.png`,
      hasPackageJson: true,
      scripts: packageJson.scripts || {}
    };
  } catch (error) {
    console.warn(`Error reading package.json for ${projectPath}:`, error.message);
    return null;
  }
}

// Function to scan directories
function scanWebsites() {
  const sites = [];
  
  try {
    const entries = fs.readdirSync(WEBSITES_DIR, { withFileTypes: true });
    
    for (const entry of entries) {
      if (!entry.isDirectory()) continue;
      
      const dirName = entry.name;
      
      // Skip certain directories
      if (dirName.startsWith('.') || 
          dirName === 'node_modules' || 
          dirName === 'codegrid-showcase' ||
          dirName.startsWith('CGMWT')) {
        continue;
      }
      
      const projectPath = path.join(WEBSITES_DIR, dirName);
      const metadata = extractMetadata(projectPath);
      
      if (metadata) {
        sites.push(metadata);
        console.log(`✓ Found: ${metadata.name} (${metadata.framework})`);
      } else {
        console.log(`⚠ Skipped: ${dirName} (no package.json or invalid)`);
      }
    }
    
    return sites;
  } catch (error) {
    console.error('Error scanning websites directory:', error);
    return [];
  }
}

// Main execution
function main() {
  console.log('🔍 Scanning for websites...');
  console.log(`📁 Scanning directory: ${WEBSITES_DIR}`);
  
  const sites = scanWebsites();
  
  if (sites.length === 0) {
    console.log('❌ No valid websites found');
    return;
  }
  
  // Sort sites by name
  sites.sort((a, b) => a.name.localeCompare(b.name));
  
  // Write to output file
  const output = {
    generated: new Date().toISOString(),
    count: sites.length,
    sites
  };
  
  fs.writeFileSync(OUTPUT_FILE, JSON.stringify(output, null, 2));
  
  console.log(`✅ Generated sites manifest: ${OUTPUT_FILE}`);
  console.log(`📊 Found ${sites.length} websites:`);
  
  sites.forEach(site => {
    console.log(`   • ${site.name} (${site.framework}) - Port ${site.port}`);
  });
}

if (require.main === module) {
  main();
}

module.exports = { scanWebsites, extractMetadata };
