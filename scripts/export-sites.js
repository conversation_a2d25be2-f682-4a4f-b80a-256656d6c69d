#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

// Configuration
const SITES_MANIFEST = path.resolve(__dirname, '../src/generated/sites.json');
const OUTPUT_DIR = path.resolve(__dirname, '../public/static-sites');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Function to detect build command for a project
function detectBuildCommand(projectPath, framework, scripts) {
  const commands = {
    nextjs: ['npm run build && npm run export', 'next build && next export', 'bun run build'],
    react: ['npm run build', 'yarn build', 'bun run build'],
    vue: ['npm run build', 'yarn build', 'bun run build'],
    vite: ['npm run build', 'yarn build', 'bun run build'],
    vanilla: ['npm run build', 'yarn build', 'bun run build']
  };

  // Check if project has specific build scripts
  if (scripts.export) return scripts.export;
  if (scripts.build) return scripts.build;
  
  // Use framework defaults
  const frameworkCommands = commands[framework] || commands.vanilla;
  
  // Check which package manager is available
  if (fs.existsSync(path.join(projectPath, 'bun.lockb'))) {
    return frameworkCommands.find(cmd => cmd.includes('bun')) || frameworkCommands[0].replace('npm', 'bun');
  }
  if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) {
    return frameworkCommands.find(cmd => cmd.includes('yarn')) || frameworkCommands[0].replace('npm', 'yarn');
  }
  
  return frameworkCommands[0];
}

// Function to find build output directory
function findBuildOutput(projectPath, framework) {
  const possibleDirs = {
    nextjs: ['out', 'dist', 'build'],
    react: ['build', 'dist'],
    vue: ['dist', 'build'],
    vite: ['dist', 'build'],
    vanilla: ['dist', 'build', 'public']
  };

  const dirs = possibleDirs[framework] || possibleDirs.vanilla;
  
  for (const dir of dirs) {
    const fullPath = path.join(projectPath, dir);
    if (fs.existsSync(fullPath)) {
      return fullPath;
    }
  }
  
  return null;
}

// Function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Function to export a single site
async function exportSite(site) {
  console.log(`\n🔨 Building ${site.name}...`);
  
  const projectPath = site.path;
  const outputPath = path.join(OUTPUT_DIR, site.slug);
  
  try {
    // Change to project directory
    process.chdir(projectPath);
    
    // Install dependencies if needed
    if (!fs.existsSync(path.join(projectPath, 'node_modules'))) {
      console.log(`📦 Installing dependencies for ${site.name}...`);
      
      let installCmd = 'npm install';
      if (fs.existsSync(path.join(projectPath, 'bun.lockb'))) {
        installCmd = 'bun install';
      } else if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) {
        installCmd = 'yarn install';
      }
      
      execSync(installCmd, { stdio: 'inherit' });
    }
    
    // Build the project
    const buildCommand = detectBuildCommand(projectPath, site.framework, site.scripts);
    console.log(`🏗️  Running: ${buildCommand}`);
    
    execSync(buildCommand, { stdio: 'inherit' });
    
    // Find build output
    const buildOutput = findBuildOutput(projectPath, site.framework);
    
    if (!buildOutput) {
      throw new Error(`Build output directory not found for ${site.name}`);
    }
    
    // Copy to static sites directory
    console.log(`📁 Copying ${buildOutput} to ${outputPath}`);
    
    if (fs.existsSync(outputPath)) {
      fs.rmSync(outputPath, { recursive: true });
    }
    
    copyDir(buildOutput, outputPath);
    
    // Create index.html if it doesn't exist
    const indexPath = path.join(outputPath, 'index.html');
    if (!fs.existsSync(indexPath)) {
      // Look for other HTML files
      const htmlFiles = fs.readdirSync(outputPath).filter(f => f.endsWith('.html'));
      if (htmlFiles.length > 0) {
        fs.copyFileSync(path.join(outputPath, htmlFiles[0]), indexPath);
      }
    }
    
    console.log(`✅ Successfully exported ${site.name}`);
    return true;
    
  } catch (error) {
    console.error(`❌ Failed to export ${site.name}:`, error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting site export process...');
  
  // Load sites manifest
  if (!fs.existsSync(SITES_MANIFEST)) {
    console.error('❌ Sites manifest not found. Run collect-sites.js first.');
    process.exit(1);
  }
  
  const manifest = JSON.parse(fs.readFileSync(SITES_MANIFEST, 'utf8'));
  const sites = manifest.sites;
  
  console.log(`📊 Found ${sites.length} sites to export`);
  
  const results = [];
  
  for (const site of sites) {
    const success = await exportSite(site);
    results.push({ site: site.name, success });
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n📋 Export Summary:');
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed > 0) {
    console.log('\nFailed exports:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   • ${r.site}`);
    });
  }
  
  console.log(`\n🎉 Export complete! Static sites available in: ${OUTPUT_DIR}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { exportSite, detectBuildCommand, findBuildOutput };
