import { BaseHubConfig } from 'basehub';

export const basehubConfig: BaseHubConfig = {
  // ALIAS Showcase Content Schema
  collections: {
    // Business Profiles (ALIAS, ARA Property Services, etc.)
    businessProfiles: {
      name: 'Business Profiles',
      slug: 'business-profiles',
      schema: {
        id: { type: 'string', required: true },
        name: { type: 'string', required: true },
        tagline: { type: 'string' },
        description: { type: 'richText' },
        logo: { type: 'image' },
        primaryColor: { type: 'color', default: '#0066FF' },
        secondaryColor: { type: 'color', default: '#00D4FF' },
        active: { type: 'boolean', default: true }
      }
    },

    // Hero Sections
    heroSections: {
      name: 'Hero Sections',
      slug: 'hero-sections',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        headline: { type: 'string', required: true },
        subheadline: { type: 'string' },
        ctaText: { type: 'string', default: 'Get Started' },
        ctaLink: { type: 'string' },
        backgroundImage: { type: 'image' },
        backgroundVideo: { type: 'string' }
      }
    },

    // About Sections
    aboutSections: {
      name: 'About Sections',
      slug: 'about-sections',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        title: { type: 'string', required: true },
        content: { type: 'richText' },
        philosophy: { type: 'richText' },
        mission: { type: 'richText' },
        values: { type: 'array', of: { type: 'string' } },
        image: { type: 'image' }
      }
    },

    // Services
    services: {
      name: 'Services',
      slug: 'services',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        title: { type: 'string', required: true },
        description: { type: 'richText' },
        icon: { type: 'string' },
        features: { type: 'array', of: { type: 'string' } },
        image: { type: 'image' },
        order: { type: 'number', default: 0 }
      }
    },

    // Portfolio Projects
    portfolioProjects: {
      name: 'Portfolio Projects',
      slug: 'portfolio-projects',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        title: { type: 'string', required: true },
        client: { type: 'string' },
        category: { type: 'string' },
        description: { type: 'richText' },
        thumbnail: { type: 'image' },
        images: { type: 'array', of: { type: 'image' } },
        videoUrl: { type: 'string' },
        liveUrl: { type: 'string' },
        tags: { type: 'array', of: { type: 'string' } },
        featured: { type: 'boolean', default: false },
        date: { type: 'date' }
      }
    },

    // Testimonials
    testimonials: {
      name: 'Testimonials',
      slug: 'testimonials',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        author: { type: 'string', required: true },
        position: { type: 'string' },
        company: { type: 'string' },
        content: { type: 'richText' },
        avatar: { type: 'image' },
        rating: { type: 'number', min: 1, max: 5 }
      }
    },

    // Team Members
    teamMembers: {
      name: 'Team Members',
      slug: 'team-members',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        name: { type: 'string', required: true },
        position: { type: 'string' },
        bio: { type: 'richText' },
        photo: { type: 'image' },
        social: {
          type: 'object',
          fields: {
            linkedin: { type: 'string' },
            twitter: { type: 'string' },
            github: { type: 'string' },
            email: { type: 'string' }
          }
        }
      }
    },

    // Contact Information
    contactInfo: {
      name: 'Contact Information',
      slug: 'contact-info',
      schema: {
        profileId: { type: 'reference', collection: 'businessProfiles' },
        email: { type: 'string' },
        phone: { type: 'string' },
        address: { type: 'richText' },
        businessHours: { type: 'richText' },
        socialMedia: {
          type: 'object',
          fields: {
            facebook: { type: 'string' },
            twitter: { type: 'string' },
            instagram: { type: 'string' },
            linkedin: { type: 'string' },
            youtube: { type: 'string' }
          }
        }
      }
    },

    // Global Settings
    globalSettings: {
      name: 'Global Settings',
      slug: 'global-settings',
      singleton: true,
      schema: {
        currentProfile: { type: 'reference', collection: 'businessProfiles' },
        maintenanceMode: { type: 'boolean', default: false },
        announcementBar: {
          type: 'object',
          fields: {
            enabled: { type: 'boolean', default: false },
            text: { type: 'string' },
            link: { type: 'string' }
          }
        }
      }
    }
  }
};