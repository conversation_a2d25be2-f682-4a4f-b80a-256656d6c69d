# AGENTS
Quick reference for automated agents working in this repo.

## Commands
• Dev server: `npm run dev`
• Production build: `npm run build`
• Lint all files: `npm run lint`
• Start built app: `npm run start`
• Tests: **none yet** – when Jest/Vitest is added expose `npm test`; run a single test with `npm test -- path/to/file.test.ts`

## Code style
• 2-space indent, single quotes, trailing commas, no semicolons (Next/Prettier defaults)
• Import order: (1) Node/React, (2) third-party, (3) `@/*` aliases, (4) relative paths; blank line between groups.
• Prefer named exports; default export only for React components/pages.
• Components & filenames: PascalCase; hooks camelCase starting with `use`.
• TypeScript strict; avoid `any`; derive types or generics; path alias `@/*`.
• Mark client components with `'use client'` at top.
• State via React hooks/Zustand; no direct mutation.
• Error handling: wrap async in `try/catch`, throw `new Error`; log via `console.error`.
• Styling with <PERSON><PERSON><PERSON>; avoid inline styles except dynamic tokens.

(No .cursor or Copilot rule files present.)