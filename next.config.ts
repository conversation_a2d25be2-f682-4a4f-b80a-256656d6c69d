import type { NextConfig } from "next";
import withBundleAnalyzer from "@next/bundle-analyzer";

const nextConfig: NextConfig = {
  // Turbopack configuration (now stable)
  turbopack: {
    rules: {
      // Add any Turbopack-specific rules here if needed
    },
  },
  
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            value:
              "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; connect-src 'self' https: http://localhost:*; img-src 'self' data: https: http://localhost:*; style-src 'self' 'unsafe-inline' https:; frame-src 'self' http://localhost:*; frame-ancestors 'self';",
          },
          { key: "Referrer-Policy", value: "strict-origin-when-cross-origin" },
          { key: "X-Content-Type-Options", value: "nosniff" },
          { key: "X-Frame-Options", value: "DENY" },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()",
          },
        ],
      },
    ];
  },
};

// Only apply bundle analyzer for production builds (which use webpack)
// Skip it during development with Turbopack
const finalConfig = process.env.NODE_ENV === 'development' 
  ? nextConfig 
  : withBundleAnalyzer({
      enabled: process.env.ANALYZE === "true",
    })(nextConfig);

export default finalConfig;
