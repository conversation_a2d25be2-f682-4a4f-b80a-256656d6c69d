{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "ANALYZE"]}, "build:analyze": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "ANALYZE=true"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV"]}, "lint": {"outputs": [], "inputs": ["src/**/*.{js,jsx,ts,tsx}", "app/**/*.{js,jsx,ts,tsx}"]}, "typecheck": {"outputs": [], "inputs": ["src/**/*.{ts,tsx}", "app/**/*.{ts,tsx}", "*.ts", "*.tsx"]}, "export-siblings": {"dependsOn": ["collect-sites"], "outputs": ["public/static-sites/**"]}, "collect-sites": {"outputs": ["src/generated/**"], "inputs": ["../../**/package.json"]}, "format": {"outputs": [], "inputs": ["src/**/*.{js,jsx,ts,tsx}", "app/**/*.{js,jsx,ts,tsx}"]}}, "globalDependencies": ["package.json", "turbo.json", "next.config.js", "tailwind.config.js"]}