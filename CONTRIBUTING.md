# Contributing to CodeGRID Showcase

Thank you for your interest in contributing to the CodeGRID Showcase project! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- **Node.js**: Version 20 or higher
- **Bun**: Latest version for package management
- **Git**: For version control
- **VS Code**: Recommended editor with extensions

### Development Setup

1. **Fork and clone the repository**:
```bash
git clone https://github.com/your-username/codegrid-showcase.git
cd codegrid-showcase
```

2. **Install dependencies**:
```bash
bun install
```

3. **Set up environment variables**:
```bash
cp .env.example .env.local
# Add your Basehub API keys
```

4. **Start development server**:
```bash
bun dev
```

## 🏗 Project Architecture

### Directory Structure

```
codegrid-showcase/
├── app/                    # Next.js App Router
│   ├── (showcase)/        # Route group for main showcase
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── src/
│   ├── components/        # React components
│   ├── ui/               # Design system components
│   ├── data/             # Static data and configurations
│   ├── lib/              # Utility functions
│   └── generated/        # Auto-generated files
├── scripts/              # Build and utility scripts
├── public/               # Static assets
└── tests/                # Test files
```

### Key Technologies

- **Next.js 15**: App Router with TypeScript
- **React 19**: Latest React features
- **Tailwind CSS**: Utility-first styling
- **Bun**: Fast package manager and runtime
- **Turbo**: Monorepo build system
- **Jest**: Testing framework
- **ESLint**: Code linting
- **Prettier**: Code formatting

## 📝 Development Guidelines

### Code Style

We use automated tools to maintain consistent code style:

- **ESLint**: Enforces code quality rules
- **Prettier**: Handles code formatting
- **TypeScript**: Provides type safety
- **Husky**: Pre-commit hooks for quality gates

### Coding Standards

#### TypeScript
- Use strict TypeScript configuration
- Define proper interfaces and types
- Avoid `any` type unless absolutely necessary
- Use meaningful variable and function names

#### React Components
- Use functional components with hooks
- Implement proper prop types with TypeScript interfaces
- Use `memo()` for performance optimization when needed
- Follow the single responsibility principle

#### Styling
- Use Tailwind CSS utility classes
- Follow the design system tokens
- Create reusable UI components in `src/ui/`
- Avoid inline styles

#### File Organization
- Group related files in appropriate directories
- Use descriptive file names
- Export components from index files
- Keep components small and focused

### Git Workflow

#### Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Test additions/updates

#### Commit Messages
Follow conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build process or auxiliary tool changes

Examples:
```bash
feat(ui): add new Button component variants
fix(api): resolve data fetching issue in website collection
docs(readme): update installation instructions
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
bun test

# Run tests in watch mode
bun test:watch

# Run tests with coverage
bun test:coverage
```

### Writing Tests

#### Component Tests
```typescript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '../ui/Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    await user.click(screen.getByRole('button'));
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

#### Utility Tests
```typescript
import { extractMetadata } from '../scripts/collect-sites';

describe('extractMetadata', () => {
  it('extracts correct metadata from package.json', () => {
    const result = extractMetadata('/test/path');
    expect(result).toEqual({
      name: 'Expected Name',
      framework: 'nextjs',
      // ... other expected properties
    });
  });
});
```

### Test Coverage

Maintain minimum 70% test coverage for:
- All UI components
- Utility functions
- API routes
- Critical business logic

## 🔧 Quality Assurance

### Pre-commit Checks

Before committing, ensure all checks pass:

```bash
# Linting
bun run lint

# Type checking
bun run typecheck

# Tests
bun run test

# Formatting
bun run format
```

### Performance Guidelines

- Use `next/dynamic` for code splitting
- Optimize images with Next.js Image component
- Implement proper caching strategies
- Monitor bundle size with `bun run analyze`
- Follow Core Web Vitals best practices

## 🚀 Deployment

### Preview Deployments

- All pull requests automatically get preview deployments
- Preview URLs are posted as PR comments
- Test your changes on the preview before merging

### Production Deployment

- Only maintainers can deploy to production
- Deployments happen automatically on main branch merges
- All CI checks must pass before deployment

## 📋 Pull Request Process

### Before Submitting

1. **Create a feature branch** from `main`
2. **Make your changes** following the guidelines
3. **Add tests** for new functionality
4. **Update documentation** if needed
5. **Run quality checks** locally
6. **Test your changes** thoroughly

### PR Requirements

- [ ] Clear description of changes
- [ ] Tests added/updated for new functionality
- [ ] Documentation updated if needed
- [ ] All CI checks passing
- [ ] No merge conflicts with main branch
- [ ] Code review from at least one maintainer

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests added/updated
- [ ] Manual testing completed
- [ ] All tests passing

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes
```

## 🐛 Bug Reports

### Before Reporting

1. **Search existing issues** to avoid duplicates
2. **Test with latest version** to ensure bug still exists
3. **Gather relevant information** about your environment

### Bug Report Template

```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., macOS 14.0]
- Browser: [e.g., Chrome 120]
- Node.js: [e.g., 20.10.0]
- Bun: [e.g., 1.0.0]

## Additional Context
Any other relevant information
```

## 💡 Feature Requests

### Before Requesting

1. **Check existing issues** for similar requests
2. **Consider the scope** and alignment with project goals
3. **Think about implementation** complexity

### Feature Request Template

```markdown
## Feature Description
Clear description of the proposed feature

## Problem Statement
What problem does this solve?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other solutions you've considered

## Additional Context
Any other relevant information
```

## 🤝 Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences

### Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Code Reviews**: For learning and knowledge sharing

## 📚 Resources

### Documentation
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

### Tools
- [VS Code Extensions](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)
- [React Developer Tools](https://react.dev/learn/react-developer-tools)
- [Bun Documentation](https://bun.sh/docs)

Thank you for contributing to CodeGRID Showcase! 🎉
