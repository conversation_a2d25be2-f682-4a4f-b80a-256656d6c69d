# CodeGrid Aggregator Showcase – Comprehensive Task List

> Use this markdown checklist to track the full migration from multi-process demos to a single Next 14 App-Router showcase, **plus full dependency upgrades**.

---

## 0 — Repo Hygiene & Audit
- [ ] Remove all checked-in `.next/`, `.turbo/`, and `dist/` folders
- [ ] Add build artefact patterns to `.gitignore`
- [ ] Delete legacy start/stop scripts (`start-all-websites*.js`, `stop-all.sh`) once new showcase is live
- [ ] Delete or archive unused WIP directories (`content-manager`, `my-blocks`, `playground`, etc.)
- [ ] Commit a fresh `bun.lock`, `pnpm-lock.yaml`, or `package-lock.json` after upgrades

## 1 — Upgrade Toolchain & Dependencies
- [ ] Install **Node 20 LTS** (update `.nvmrc` if present)
- [ ] Adopt **pnpm 8** (or npm 10) and enable workspaces if needed
- [ ] Run `pnpm up --latest` to bump all deps; resolve peer-dep warnings
- [ ] Upgrade core stack:
  - [ ] `next@14.x`  
  - [ ] `react@18.3.x` or latest stable (`19.x` when GA)  
  - [ ] `typescript@^5.5`  
  - [ ] `tailwindcss@^3.4`  
  - [ ] `eslint@^9` & `eslint-plugin-next@latest`  
  - [ ] `zustand@^4.5`, `three@^0.162`, `@react-three/fiber@^9`
- [ ] Regenerate shadcn-ui components via `npx shadcn-ui@latest init`
- [ ] Remove duplicated `@types/*`

## 2 — Monorepo / Package Setup
- [ ] Keep **single** `package.json` at `codegrid-showcase` root
- [ ] Add `turbo.json` for incremental builds + remote cache
- [ ] Configure `prettier`, `biome`, and `husky` hooks (as per global project prefs)

## 3 — Generator Scripts
- [ ] `scripts/collect-sites.ts`  
  - [ ] Scan sibling directories inside `../Websites/*` (exclude `codegrid-showcase`, `node_modules`, hidden files)
  - [ ] Extract `slug`, `title`, `description`, `screenshot` path
  - [ ] Emit JSON to `src/generated/sites.json`
- [ ] `scripts/export-site.ts`  
  - [ ] For each slug, run that project’s static export (`next export`, `vite build`, or custom)  
  - [ ] Copy resulting files to `public/static-sites/[slug]/`
- [ ] Wire both scripts into `"export-siblings"` and `"build"` npm scripts

## 4 — Next.js Route Restructure
- [ ] Create route group `app/(showcase)`
  - [ ] `page.tsx` – grid of sites using manifest
  - [ ] `[slug]/page.tsx` – loads iframe (`/static-sites/[slug]/index.html`)
  - [ ] Shared `layout.tsx` – header, footer, theme provider
- [ ] Implement `generateStaticParams()` for `[slug]` routes
- [ ] Add graceful error UI for missing export

## 5 — Design System Consolidation
- [ ] Move primitives to `src/ui/*` and export barrel
- [ ] Centralise Tailwind config (`tailwind.config.js`) – include token presets
- [ ] Ensure dark-mode classes use `class="dark"` pattern

## 6 — Performance Optimisations
- [ ] Lazy-import heavy Three.js components with `next/dynamic` (`ssr:false`)
- [ ] Set `export const dynamic = "error"` on purely static pages
- [ ] Use `Edge Runtime` only for BaseHub fetches (if still required)
- [ ] Enable **ISR** via `export const revalidate = 60` for dynamic content pages
- [ ] Add `next/headers` Server-Timing for performance tracing

## 7 — CI / CD
- [ ] Update GitHub Actions (or other) workflow:
  - [ ] Setup Node 20  
  - [ ] Cache pnpm store and Turbo cache  
  - [ ] Run `pnpm run lint && pnpm run typecheck`
  - [ ] Run `pnpm run build` (includes sibling exports)
- [ ] Deploy to **Vercel** with edge functions enabled
- [ ] Add lighthouse-ci job for performance budgets

## 8 — Quality Gates
- [ ] ESLint passes (no `any`, follows Airbnb rules)
- [ ] `pnpm run test` (add vitest/Jest if not present)
- [ ] Bundle-analysis: ensure each JS chunk < 250 kB gzip

## 9 — Documentation
- [ ] Update `README.md` with new architecture overview and local dev instructions
- [ ] Add CONTRIBUTING section for adding new demo sites
- [ ] Document environment variables (`.env.example`)

---

**Legend**: Tasks are intentionally fine-grained – tick each once completed. Adjust or re-order as necessary.
