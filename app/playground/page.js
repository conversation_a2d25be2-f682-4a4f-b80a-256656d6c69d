"use client";

export default function PlaygroundPage() {
  return (
    <div style={{ minHeight: "100vh", padding: "2rem" }}>
      <div className="container">
        <h1>Live Playground</h1>
        <p style={{ fontSize: "1.2rem", marginTop: "1rem", opacity: 0.8 }}>
          Experiment with components in real-time
        </p>
        
        <div style={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "2rem",
          marginTop: "3rem",
          minHeight: "600px"
        }}>
          <div style={{
            background: "var(--color-surface)",
            padding: "2rem",
            borderRadius: "0.5rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center"
          }}>
            <p style={{ opacity: 0.5 }}>Code Editor</p>
          </div>
          
          <div style={{
            background: "var(--color-surface)",
            padding: "2rem",
            borderRadius: "0.5rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center"
          }}>
            <p style={{ opacity: 0.5 }}>Preview</p>
          </div>
        </div>
      </div>
    </div>
  );
}
