:root {
  --color-background: #0a0a0a;
  --color-foreground: #ededed;
  --color-surface: #141414;
  --color-surface-light: #1f1f1f;
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --color-success: #10b981;
  --color-border: #262626;
  --color-text-secondary: #a3a3a3;
  
  /* Animation vars */
  --animation-primary: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background: var(--color-background);
  color: var(--color-foreground);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}


/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(120px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  top: -300px;
  left: -300px;
  animation-delay: 0s;
}

.gradient-orb-2 {
  width: 800px;
  height: 800px;
  background: linear-gradient(135deg, #f43f5e 0%, #f59e0b 100%);
  bottom: -400px;
  right: -400px;
  animation-delay: 7s;
}

.gradient-orb-3 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 14s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

/* Showcase Container */
.showcase-container {
  min-height: 100vh;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Header */
.showcase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--color-border);
  position: relative;
}

.header-title {
  font-size: 3.5rem;
  font-weight: 700;
  display: flex;
  gap: 0.5rem;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.title-word {
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.title-accent {
  color: var(--color-foreground);
  opacity: 0.3;
  font-weight: 300;
}

.header-subtitle {
  font-size: 1.25rem;
  color: var(--color-text-secondary);
}

.header-nav {
  display: flex;
  gap: 2.5rem;
}

.nav-link {
  position: relative;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
  cursor: pointer;
}

.nav-text {
  display: block;
  transition: transform 0.3s ease;
}

.nav-line {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

.nav-link:hover .nav-text {
  color: var(--color-foreground);
  transform: translateY(-2px);
}

.nav-link:hover .nav-line {
  width: 100%;
}

/* Categories */
.categories {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.category-btn {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.category-btn:hover {
  background: var(--color-surface-light);
  color: var(--color-foreground);
  border-color: rgba(255, 255, 255, 0.1);
}

.category-btn.active {
  background: var(--color-surface-light);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.category-icon {
  font-size: 1.25rem;
}

.category-count {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
}

/* Website Grid */
.websites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.website-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.card-gradient {
  display: none;
}

.website-card:hover {
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1), 
              0 4px 16px rgba(0, 0, 0, 0.3);
}


.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  z-index: 1;
}

.card-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: rgba(255, 255, 255, 0.02);
}

.card-icon {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.4s ease;
}

.card-initial {
  font-size: 3rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
}

.card-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  z-index: 2;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success);
  box-shadow: 0 0 10px var(--color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.card-content {
  padding: 1.75rem;
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.card-tagline {
  color: var(--color-primary);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-description {
  color: var(--color-text-secondary);
  font-size: 0.9375rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.8125rem;
}

.feature-dot {
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
}

.card-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  transition: all 0.3s ease;
}

.website-card:hover .tech-tag {
  border-color: rgba(99, 102, 241, 0.4);
  color: var(--color-foreground);
}

.card-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.625rem;
  font-weight: 500;
  transition: all 0.3s var(--animation-primary);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
}

.btn-text {
  position: relative;
  z-index: 1;
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover .btn-arrow {
  transform: translateX(4px);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-foreground);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Footer */
.showcase-footer {
  text-align: center;
  padding: 3rem 0 2rem;
  border-top: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

.footer-title {
  font-size: 1.5rem;
  color: var(--color-foreground);
  margin-bottom: 0.5rem;
}

.footer-subtitle {
  font-size: 1rem;
  margin-bottom: 2rem;
  opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
  body {
    cursor: auto;
  }

  .custom-cursor,
  .cursor-dot {
    display: none;
  }

  .showcase-container {
    padding: 1rem;
  }

  .header-title {
    font-size: 2.5rem;
  }

  .showcase-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-nav {
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .websites-grid {
    grid-template-columns: 1fr;
  }

  .categories {
    overflow-x: auto;
    flex-wrap: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
  }

  .categories::-webkit-scrollbar {
    display: none;
  }
}