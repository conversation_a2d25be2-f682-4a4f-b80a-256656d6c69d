import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { websites } from '../../../../src/data/websites';

const execAsync = promisify(exec);

export async function GET() {
  const diagnostics = {
    totalSites: websites.length,
    sites: []
  };

  // Check a sample of sites
  const sampleSites = websites.slice(0, 5);
  
  for (const website of sampleSites) {
    const projectPath = `/Users/<USER>/Downloads/CodeGRID/Websites/${website.path}`;
    const siteInfo = {
      id: website.id,
      name: website.name,
      path: projectPath,
      port: website.port,
      exists: existsSync(projectPath),
      hasPackageJson: false,
      packageManager: null,
      hasDependencies: false,
      portInUse: false
    };

    if (siteInfo.exists) {
      siteInfo.hasPackageJson = existsSync(`${projectPath}/package.json`);
      
      if (siteInfo.hasPackageJson) {
        // Check package manager
        if (existsSync(`${projectPath}/bun.lockb`)) {
          siteInfo.packageManager = 'bun';
        } else if (existsSync(`${projectPath}/yarn.lock`)) {
          siteInfo.packageManager = 'yarn';
        } else if (existsSync(`${projectPath}/package-lock.json`)) {
          siteInfo.packageManager = 'npm';
        }
        
        // Check if dependencies are installed
        siteInfo.hasDependencies = existsSync(`${projectPath}/node_modules`);
      }

      // Check if port is in use
      try {
        await execAsync(`lsof -ti:${website.port}`);
        siteInfo.portInUse = true;
      } catch {
        siteInfo.portInUse = false;
      }
    }

    diagnostics.sites.push(siteInfo);
  }

  // Check system commands
  const commands = ['npm', 'bun', 'yarn', 'nc', 'lsof'];
  diagnostics.systemCommands = {};
  
  for (const cmd of commands) {
    try {
      await execAsync(`which ${cmd}`);
      diagnostics.systemCommands[cmd] = true;
    } catch {
      diagnostics.systemCommands[cmd] = false;
    }
  }

  return NextResponse.json(diagnostics);
}