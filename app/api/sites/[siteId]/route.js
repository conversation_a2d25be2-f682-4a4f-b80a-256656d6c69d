import { NextResponse } from 'next/server';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { websites } from '../../../../src/data/websites';

const execAsync = promisify(exec);

// Track running sites and their processes
const runningSites = new Map();
const runningProcesses = new Map();

export async function GET(request, { params }) {
  const { siteId } = params;
  const website = websites.find(w => w.id === siteId);
  
  if (!website) {
    return NextResponse.json({ error: 'Website not found' }, { status: 404 });
  }

  const isRunning = runningSites.has(siteId);
  
  return NextResponse.json({
    id: siteId,
    name: website.name,
    port: website.port,
    isRunning,
    url: isRunning ? `http://localhost:${website.port}` : null
  });
}

export async function POST(request, { params }) {
  const { siteId } = params;
  const { action } = await request.json();
  
  const website = websites.find(w => w.id === siteId);
  
  if (!website) {
    return NextResponse.json({ error: 'Website not found' }, { status: 404 });
  }

  try {
    if (action === 'start') {
      // Check if already running
      if (runningSites.has(siteId)) {
        // Check if port is actually in use
        try {
          await execAsync(`lsof -ti:${website.port}`);
          return NextResponse.json({ 
            message: 'Site already running',
            url: `http://localhost:${website.port}`
          });
        } catch {
          // Port not in use, clean up stale entry
          runningSites.delete(siteId);
          runningProcesses.delete(siteId);
        }
      }

      // Start the site
      const projectPath = `/Users/<USER>/Downloads/CodeGRID/Websites/${website.path}`;
      
      // Verify project path exists
      if (!existsSync(projectPath)) {
        return NextResponse.json({ 
          error: 'Project directory not found',
          path: projectPath 
        }, { status: 404 });
      }

      // Check for package.json to determine package manager
      const hasPackageJson = existsSync(`${projectPath}/package.json`);
      
      // Determine package manager and start command
      let command, args;
      if (website.tech.some(t => t.includes('Vite'))) {
        if (!hasPackageJson) {
          return NextResponse.json({ 
            error: 'No package.json found for Vite project',
            path: projectPath 
          }, { status: 400 });
        }
        // Check which package manager to use
        const hasBunLock = existsSync(`${projectPath}/bun.lockb`);
        const hasYarnLock = existsSync(`${projectPath}/yarn.lock`);
        
        if (hasBunLock) {
          command = 'bun';
          args = ['run', 'dev', '--', '--port', website.port.toString()];
        } else if (hasYarnLock) {
          command = 'yarn';
          args = ['dev', '--port', website.port.toString()];
        } else {
          command = 'npm';
          args = ['run', 'dev', '--', '--port', website.port.toString()];
        }
      } else if (website.tech.some(t => t.includes('Next.js'))) {
        if (!hasPackageJson) {
          return NextResponse.json({ 
            error: 'No package.json found for Next.js project',
            path: projectPath 
          }, { status: 400 });
        }
        // Check which package manager to use
        const hasBunLock = existsSync(`${projectPath}/bun.lockb`);
        const hasYarnLock = existsSync(`${projectPath}/yarn.lock`);
        
        if (hasBunLock) {
          command = 'bun';
          args = ['run', 'dev', '--', '-p', website.port.toString()];
        } else if (hasYarnLock) {
          command = 'yarn';
          args = ['dev', '-p', website.port.toString()];
        } else {
          command = 'npm';
          args = ['run', 'dev', '--', '-p', website.port.toString()];
        }
      } else {
        // Static HTML sites
        command = 'npx';
        args = ['http-server', '-p', website.port.toString(), '-c-1'];
      }

      console.log(`Starting ${siteId} with command: ${command} ${args.join(' ')} in ${projectPath}`);

      // Spawn process instead of exec for better control
      const childProcess = spawn(command, args, {
        cwd: projectPath,
        stdio: 'pipe',
        shell: true
      });

      // Store process reference
      runningProcesses.set(siteId, childProcess);

      // Handle process output
      childProcess.stdout.on('data', (data) => {
        console.log(`[${siteId}] stdout: ${data}`);
      });

      childProcess.stderr.on('data', (data) => {
        console.error(`[${siteId}] stderr: ${data}`);
      });

      childProcess.on('error', (error) => {
        console.error(`[${siteId}] Failed to start:`, error);
        runningSites.delete(siteId);
        runningProcesses.delete(siteId);
      });

      childProcess.on('exit', (code, signal) => {
        console.log(`[${siteId}] Process exited with code ${code} and signal ${signal}`);
        runningSites.delete(siteId);
        runningProcesses.delete(siteId);
      });

      // Store site info
      runningSites.set(siteId, {
        startedAt: new Date(),
        port: website.port,
        pid: childProcess.pid
      });

      // Wait for server to start
      let retries = 0;
      const maxRetries = 10;
      const checkInterval = 1000;

      while (retries < maxRetries) {
        try {
          // Check if port is open
          await execAsync(`nc -z localhost ${website.port}`);
          console.log(`[${siteId}] Server is ready on port ${website.port}`);
          break;
        } catch {
          retries++;
          if (retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
          }
        }
      }

      if (retries === maxRetries) {
        // Kill the process if it didn't start properly
        if (childProcess && !childProcess.killed) {
          childProcess.kill();
        }
        runningSites.delete(siteId);
        runningProcesses.delete(siteId);
        
        return NextResponse.json({ 
          error: 'Server failed to start within timeout',
          details: 'Check if dependencies are installed'
        }, { status: 500 });
      }

      return NextResponse.json({
        message: 'Site started successfully',
        url: `http://localhost:${website.port}`,
        pid: childProcess.pid
      });
      
    } else if (action === 'stop') {
      // Stop the site
      if (!runningSites.has(siteId)) {
        return NextResponse.json({ message: 'Site not running' });
      }

      // Try to kill the process we spawned
      const childProcess = runningProcesses.get(siteId);
      if (childProcess && !childProcess.killed) {
        childProcess.kill('SIGTERM');
        // Give it a moment to terminate gracefully
        await new Promise(resolve => setTimeout(resolve, 1000));
        if (!childProcess.killed) {
          childProcess.kill('SIGKILL');
        }
      }

      // Also kill any process on the port (in case it was started outside)
      await execAsync(`lsof -ti:${website.port} | xargs kill -9`).catch(() => {});
      
      runningSites.delete(siteId);
      runningProcesses.delete(siteId);
      
      return NextResponse.json({ message: 'Site stopped successfully' });
    }
    
  } catch (error) {
    return NextResponse.json({ 
      error: 'Failed to manage site',
      details: error.message 
    }, { status: 500 });
  }

  return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
}