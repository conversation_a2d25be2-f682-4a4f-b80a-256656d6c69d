"use client";

import { useState } from "react";
import { websites, categories } from "../../src/data/websites";
import DynamicSitePreview from "../../src/components/showcase/DynamicSitePreview";

export default function ShowcaseV2() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredWebsites = websites.filter(website => {
    const categoryMatch = selectedCategory === "All" || website.category === selectedCategory;
    const searchMatch = searchQuery === "" || 
      website.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      website.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      website.tech.some(t => t.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return categoryMatch && searchMatch;
  });

  return (
    <div className="showcase-container">
      <header className="showcase-header">
        <div className="header-content">
          <h1>Dynamic Website Showcase</h1>
          <p>Start only the sites you need, when you need them</p>
        </div>
      </header>

      <div className="showcase-controls">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search websites, technologies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="category-filters">
          {categories.map(category => (
            <button
              key={category.name}
              className={`category-btn ${selectedCategory === category.name ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category.name)}
            >
              <span className="category-icon">{category.icon}</span>
              <span>{category.name}</span>
              <span className="category-count">{category.count}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="websites-grid">
        {filteredWebsites.map(website => (
          <DynamicSitePreview key={website.id} website={website} />
        ))}
      </div>

      {filteredWebsites.length === 0 && (
        <div className="no-results">
          <p>No websites found matching your criteria</p>
        </div>
      )}

      <style jsx>{`
        .showcase-container {
          min-height: 100vh;
          background: var(--color-background);
          padding-bottom: 4rem;
        }

        .showcase-header {
          background: linear-gradient(135deg, #0066FF 0%, #00D4FF 100%);
          padding: 4rem 0;
          text-align: center;
        }

        .header-content h1 {
          font-size: 3rem;
          color: white;
          margin: 0 0 1rem 0;
        }

        .header-content p {
          font-size: 1.25rem;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
        }

        .showcase-controls {
          max-width: 1400px;
          margin: -2rem auto 3rem;
          padding: 0 2rem;
          position: relative;
          z-index: 10;
        }

        .search-bar {
          background: var(--color-surface);
          border-radius: 0.5rem;
          padding: 1rem;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          margin-bottom: 2rem;
        }

        .search-input {
          width: 100%;
          background: var(--color-background);
          border: 1px solid var(--color-border);
          border-radius: 0.25rem;
          padding: 0.75rem 1rem;
          font-size: 1rem;
          color: var(--color-foreground);
        }

        .search-input:focus {
          outline: none;
          border-color: var(--color-primary);
        }

        .category-filters {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }

        .category-btn {
          background: var(--color-surface);
          border: 1px solid var(--color-border);
          border-radius: 0.5rem;
          padding: 0.75rem 1.25rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--color-foreground);
        }

        .category-btn:hover {
          background: var(--color-surface-light);
          transform: translateY(-2px);
        }

        .category-btn.active {
          background: var(--color-primary);
          color: white;
          border-color: var(--color-primary);
        }

        .category-icon {
          font-size: 1.25rem;
        }

        .category-count {
          background: rgba(255, 255, 255, 0.2);
          padding: 0.125rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
        }

        .websites-grid {
          max-width: 1400px;
          margin: 0 auto;
          padding: 0 2rem;
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
          gap: 2rem;
        }

        .no-results {
          text-align: center;
          padding: 4rem 2rem;
          color: var(--color-text-secondary);
        }

        @media (max-width: 768px) {
          .header-content h1 {
            font-size: 2rem;
          }

          .websites-grid {
            grid-template-columns: 1fr;
          }

          .category-filters {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
}