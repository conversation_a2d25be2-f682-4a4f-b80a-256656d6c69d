"use client";

import { useParams } from "next/navigation";
import Link from "next/link";
import { useState } from "react";
import { websites } from "../../../src/data/websites";

export default function PreviewPage() {
  const params = useParams();
  const [deviceMode, setDeviceMode] = useState("desktop");
  const website = websites.find(w => w.id === params.id);

  if (!website) {
    return (
      <div className="preview-container">
        <div className="preview-error">
          <h2>Website not found</h2>
          <Link href="/" className="btn btn-primary">Back to Gallery</Link>
        </div>
      </div>
    );
  }

  const deviceConfigs = {
    desktop: {
      width: "100%",
      height: "100%",
      scale: 1,
      frame: false,
      name: "Desktop"
    },
    tablet: {
      width: 1366,
      height: 1024,
      scale: 0.65,
      frame: true,
      name: "iPad Pro 12.9\"",
      bezelColor: "#1a1a1a",
      screenRadius: "18px"
    },
    mobile: {
      width: 440,
      height: 956,
      scale: 0.8,
      frame: true,
      name: "iPhone 16 Pro Max",
      bezelColor: "#2a2a2a",
      screenRadius: "55px",
      notch: true
    }
  };

  const currentDevice = deviceConfigs[deviceMode];

  return (
    <div className="preview-container">
      <div className="preview-header">
        <div className="preview-info">
          <Link href="/" className="preview-back">← Back to Gallery</Link>
          <h1>{website.name}</h1>
          <p>{website.description}</p>
        </div>
        <div className="preview-actions">
          <a 
            href={`http://localhost:${website.port}`}
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-primary"
          >
            Open in New Tab
          </a>
        </div>
      </div>

      <div className="preview-frame-container">
        <div className="preview-device-bar">
          <div className="device-controls">
            {Object.entries(deviceConfigs).map(([key, config]) => (
              <button 
                key={key}
                className={`device-btn ${deviceMode === key ? 'active' : ''}`}
                onClick={() => setDeviceMode(key)}
              >
                {config.name}
              </button>
            ))}
          </div>
          <div className="device-url">
            localhost:{website.port}
          </div>
        </div>
        
        <div className={`preview-viewport ${deviceMode}`}>
          {currentDevice.frame ? (
            <div 
              className="device-frame"
              style={{
                width: `${currentDevice.width}px`,
                height: `${currentDevice.height}px`,
                transform: `scale(${currentDevice.scale})`
              }}
            >
              {currentDevice.notch && (
                <div className="device-notch">
                  <div className="notch"></div>
                  <div className="dynamic-island"></div>
                </div>
              )}
              <div className="device-screen">
                <iframe
                  src={`http://localhost:${website.port}`}
                  className="preview-iframe"
                  title={website.name}
                  style={{
                    width: `${currentDevice.width}px`,
                    height: `${currentDevice.height}px`
                  }}
                />
              </div>
            </div>
          ) : (
            <iframe
              src={`http://localhost:${website.port}`}
              className="preview-iframe desktop-iframe"
              title={website.name}
            />
          )}
        </div>
      </div>

      <style jsx>{`
        .preview-container {
          height: 100vh;
          display: flex;
          flex-direction: column;
          background: var(--color-background);
        }

        .preview-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          gap: 1rem;
        }

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.5rem 2rem;
          background: var(--color-surface);
          border-bottom: 1px solid var(--color-border);
        }

        .preview-info h1 {
          font-size: 1.5rem;
          margin: 0.5rem 0;
        }

        .preview-info p {
          color: var(--color-text-secondary);
          margin: 0;
        }

        .preview-back {
          color: var(--color-text-secondary);
          font-size: 0.875rem;
          transition: color 0.3s ease;
        }

        .preview-back:hover {
          color: var(--color-foreground);
        }

        .preview-frame-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          background: #0a0a0a;
          overflow: hidden;
        }

        .preview-device-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 2rem;
          background: var(--color-surface-light);
          border-bottom: 1px solid var(--color-border);
        }

        .device-controls {
          display: flex;
          gap: 0.5rem;
        }

        .device-btn {
          background: transparent;
          border: 1px solid var(--color-border);
          color: var(--color-text-secondary);
          padding: 0.5rem 1rem;
          border-radius: 0.25rem;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.875rem;
        }

        .device-btn:hover {
          background: var(--color-surface);
          color: var(--color-foreground);
        }

        .device-btn.active {
          background: var(--color-primary);
          color: white;
          border-color: var(--color-primary);
        }

        .device-url {
          font-family: monospace;
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          background: var(--color-surface);
          padding: 0.5rem 1rem;
          border-radius: 0.25rem;
        }

        .preview-viewport {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background: #050505;
          position: relative;
          overflow: auto;
        }

        .preview-viewport.desktop {
          padding: 0;
        }

        .device-frame {
          position: relative;
          background: var(--bezel-color, #1a1a1a);
          border-radius: var(--screen-radius, 18px);
          padding: 12px;
          box-shadow: 
            0 0 0 1px rgba(255, 255, 255, 0.1),
            0 10px 50px rgba(0, 0, 0, 0.5),
            0 30px 100px rgba(0, 0, 0, 0.3);
          transform-origin: center center;
          transition: transform 0.3s ease;
        }

        .preview-viewport.mobile .device-frame {
          --bezel-color: #2a2a2a;
          --screen-radius: 55px;
          padding: 10px;
        }

        .preview-viewport.tablet .device-frame {
          --bezel-color: #1a1a1a;
          --screen-radius: 18px;
          padding: 20px;
        }

        .device-notch {
          position: absolute;
          top: 10px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 10;
          pointer-events: none;
        }

        .dynamic-island {
          width: 126px;
          height: 37px;
          background: #000;
          border-radius: 24px;
          margin: 0 auto;
          position: relative;
          top: 10px;
        }

        .device-screen {
          width: 100%;
          height: 100%;
          border-radius: calc(var(--screen-radius, 18px) - 12px);
          overflow: hidden;
          position: relative;
          background: white;
        }

        .preview-viewport.mobile .device-screen {
          border-radius: 45px;
        }

        .preview-viewport.tablet .device-screen {
          border-radius: 8px;
        }

        .preview-iframe {
          width: 100%;
          height: 100%;
          border: none;
          background: white;
          display: block;
        }

        .desktop-iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        /* Device-specific styling */
        @media (max-width: 1400px) {
          .preview-viewport.tablet .device-frame {
            transform: scale(0.55);
          }
        }

        @media (max-width: 1200px) {
          .preview-viewport.tablet .device-frame {
            transform: scale(0.45);
          }
          .preview-viewport.mobile .device-frame {
            transform: scale(0.7);
          }
        }

        @media (max-width: 768px) {
          .preview-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
          }
          
          .preview-viewport {
            padding: 1rem;
          }
          
          .preview-viewport.tablet .device-frame {
            transform: scale(0.35);
          }
          
          .preview-viewport.mobile .device-frame {
            transform: scale(0.6);
          }
        }
      `}</style>
    </div>
  );
}