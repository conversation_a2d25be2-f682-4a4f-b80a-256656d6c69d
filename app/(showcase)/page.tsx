"use client";

import { useState } from "react";
import Link from "next/link";
import { websites, categories } from "../../src/data/websites";

// Enable static generation
export const dynamic = "error";

export default function Home() {
  const [activeCategory, setActiveCategory] = useState("All");
  const [hoveredCard, setHoveredCard] = useState(null);

  const filteredWebsites = websites.filter(
    website => activeCategory === "All" || website.category === activeCategory
  );



  // Category change - simple instant change
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
  };


  return (
    <div className="showcase-container">
      {/* Header */}
      <header className="showcase-header">
        <div className="header-content">
          <h1 className="header-title">
            <span className="title-word">ALIAS</span>
            <span className="title-accent">Showcase</span>
          </h1>
          <p className="header-subtitle">Digital business transformations and creative solutions</p>
        </div>
        <nav className="header-nav">
          <Link href="/showcase" className="nav-link">
            <span className="nav-text">Components</span>
            <span className="nav-line"></span>
          </Link>
          <Link href="/playground" className="nav-link">
            <span className="nav-text">Playground</span>
            <span className="nav-line"></span>
          </Link>
          <Link href="/content-manager" className="nav-link">
            <span className="nav-text">Content Manager</span>
            <span className="nav-line"></span>
          </Link>
          <a 
            href="https://github.com/alias-solutions" 
            target="_blank" 
            rel="noopener noreferrer"
            className="nav-link"
          >
            <span className="nav-text">GitHub</span>
            <span className="nav-line"></span>
          </a>
        </nav>
      </header>

      {/* Categories */}
      <div className="categories">
        {categories.map((category) => (
          <button
            key={category.name}
            className={`category-btn ${activeCategory === category.name ? "active" : ""}`}
            onClick={() => handleCategoryChange(category.name)}
          >
            <span className="category-icon">{category.icon}</span>
            <span className="category-name">{category.name}</span>
            <span className="category-count">{category.count}</span>
          </button>
        ))}
      </div>

      {/* Website Grid */}
      <div className="websites-grid">
        {filteredWebsites.map((website, index) => (
          <div
            key={website.id}
            className="website-card"
            onMouseEnter={() => setHoveredCard(website.id)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="card-gradient"></div>
            
            <div className="card-image">
              <div className="card-placeholder">
                <div className="card-icon">
                  <span className="card-initial">
                    {website.name.split('·')[0].charAt(0)}
                  </span>
                </div>
                <div className="animation-layer"></div>
              </div>
              <div className="card-status">
                <span className={`status-dot ${website.status}`}></span>
                <span className="status-text">Port {website.port}</span>
              </div>
            </div>

            <div className="card-content">
              <h3 className="card-title">{website.name}</h3>
              <p className="card-tagline">{website.tagline}</p>
              <p className="card-description">{website.description}</p>

              <div className="card-features">
                {website.features.slice(0, 2).map((feature) => (
                  <span key={feature} className="feature-tag">
                    <span className="feature-dot"></span>
                    {feature}
                  </span>
                ))}
              </div>

              <div className="card-tech">
                {website.tech.slice(0, 3).map((tech) => (
                  <span key={tech} className="tech-tag">{tech}</span>
                ))}
              </div>

              <div className="card-actions">
                <a 
                  href={`http://localhost:${website.port}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary"
                >
                  <span className="btn-text">View Live</span>
                  <span className="btn-arrow">→</span>
                </a>
                <Link 
                  href={`/preview/${website.id}`}
                  className="btn btn-secondary"
                >
                  <span className="btn-text">Preview</span>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <footer className="showcase-footer">
        <div className="footer-content">
          <p className="footer-title">ALIAS Innovation Lab</p>
          <p className="footer-subtitle">Adaptive Learning Integrated Agentic Solutions</p>
        </div>
        <div className="footer-info">
          <p>All projects tested and running • {new Date().getFullYear()}</p>
        </div>
      </footer>
    </div>
  );
}