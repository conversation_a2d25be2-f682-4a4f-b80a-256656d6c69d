import { Metadata } from 'next';
import { ProfileSwitcher } from '@/components/ProfileSwitcher';

export const metadata: Metadata = {
  title: 'ALIAS Showcase',
  description: 'Digital business transformations and creative solutions',
};

export default function ShowcaseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="showcase-layout">
      <header className="showcase-header">
        <div className="header-container">
          <div className="header-brand">
            <h1 className="brand-title">
              <span className="brand-alias">ALIAS</span>
              <span className="brand-showcase">Showcase</span>
            </h1>
          </div>
          <div className="header-actions">
            <ProfileSwitcher />
          </div>
        </div>
      </header>
      
      <main className="showcase-main">
        {children}
      </main>
      
      <footer className="showcase-footer">
        <div className="footer-content">
          <p className="footer-title">ALIAS Innovation Lab</p>
          <p className="footer-subtitle">
            Adaptive Learning Integrated Agentic Solutions
          </p>
        </div>
        <div className="footer-info">
          <p>All projects tested and running • {new Date().getFullYear()}</p>
        </div>
      </footer>
    </div>
  );
}
