"use client";

export default function ShowcasePage() {
  const categories = [
    { name: "Hero Sections", count: 12, color: "#ff6b6b" },
    { name: "Navigation", count: 8, color: "#4ecdc4" },
    { name: "Galleries", count: 10, color: "#ffe66d" },
    { name: "Text Effects", count: 15, color: "#a8e6cf" },
    { name: "Interactions", count: 20, color: "#ff8b94" },
    { name: "Page Transitions", count: 6, color: "#b4a8e6" }
  ];

  return (
    <div style={{ minHeight: "100vh", padding: "2rem" }}>
      <div className="container">
        <h1>Component Showcase</h1>
        <p style={{ fontSize: "1.2rem", marginTop: "1rem", opacity: 0.8 }}>
          Explore our collection of premium components
        </p>
        
        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
          gap: "1.5rem",
          marginTop: "3rem"
        }}>
          {categories.map((category) => (
            <div
              key={category.name}
              style={{
                background: "var(--color-surface)",
                padding: "2rem",
                borderRadius: "0.5rem",
                border: "1px solid var(--color-surface-light)",
                cursor: "pointer",
                transition: "all 0.3s ease"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = category.color;
                e.currentTarget.style.transform = "translateY(-4px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = "var(--color-surface-light)";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              <div style={{
                width: "60px",
                height: "60px",
                background: category.color,
                borderRadius: "0.5rem",
                marginBottom: "1rem"
              }} />
              <h3 style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
                {category.name}
              </h3>
              <p style={{ opacity: 0.7 }}>
                {category.count} components
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
