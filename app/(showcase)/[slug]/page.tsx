import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { websites } from '../../../src/data/websites';

// Enable static generation
export const dynamic = "error";

// Generate static params for all websites
export async function generateStaticParams() {
  return websites.map((website) => ({
    slug: website.id,
  }));
}

// Generate metadata for each page
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  const website = websites.find((w) => w.id === params.slug);
  
  if (!website) {
    return {
      title: 'Website Not Found',
    };
  }

  return {
    title: `${website.name} - ALIAS Showcase`,
    description: website.description,
  };
}

export default function WebsitePage({
  params,
}: {
  params: { slug: string };
}) {
  const website = websites.find((w) => w.id === params.slug);

  if (!website) {
    notFound();
  }

  return (
    <div className="website-page">
      <div className="website-header">
        <h1>{website.name}</h1>
        <p>{website.description}</p>
      </div>
      
      <div className="website-iframe-container">
        <iframe
          src={`/static-sites/${website.id}/index.html`}
          className="website-iframe"
          title={website.name}
          loading="lazy"
        />
      </div>
      
      <div className="website-fallback">
        <p>
          Static export not available. 
          <a 
            href={`http://localhost:${website.port}`}
            target="_blank"
            rel="noopener noreferrer"
            className="fallback-link"
          >
            View live site
          </a>
        </p>
      </div>
    </div>
  );
}
