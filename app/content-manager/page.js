'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Save, RefreshCw, Plus, Trash2, Edit3, Check, X } from 'lucide-react';
import ProfileSwitcher from '@/components/ProfileSwitcher';
import { useProfileSwitcher } from '@/hooks/useBasehub';

export default function ContentManager() {
  const { currentProfileId } = useProfileSwitcher();
  const [activeSection, setActiveSection] = useState('hero');
  const [content, setContent] = useState({});
  const [editingField, setEditingField] = useState(null);
  const [saving, setSaving] = useState(false);

  // Content sections structure
  const sections = [
    { id: 'hero', name: 'Hero Section', icon: '🏠' },
    { id: 'about', name: 'About', icon: '📝' },
    { id: 'services', name: 'Services', icon: '💼' },
    { id: 'portfolio', name: '<PERSON><PERSON><PERSON>', icon: '🎨' },
    { id: 'testimonials', name: 'Testimonials', icon: '💬' },
    { id: 'contact', name: 'Contact', icon: '📧' }
  ];

  // Sample content structure - will be replaced with Basehub data
  const sampleContent = {
    hero: {
      headline: 'Transform Your Business with Intelligent Automation',
      subheadline: 'We create digital twins of businesses, enabling deep understanding and optimization through adaptive AI systems.',
      ctaText: 'Discover Your Digital Alias',
      ctaLink: '/contact'
    },
    about: {
      title: 'About ALIAS',
      philosophy: 'At ALIAS, we believe the best way to transform a business is to first truly understand it.',
      mission: 'Create sophisticated digital representations of businesses for analysis and optimization.',
      values: ['Innovation', 'Understanding', 'Optimization', 'Transformation']
    },
    services: [
      {
        id: 1,
        title: 'Digital Twin Creation',
        description: 'We build comprehensive digital representations of your business.',
        icon: '🔮'
      },
      {
        id: 2,
        title: 'Process Optimization',
        description: 'Identify opportunities for improvement in a risk-free environment.',
        icon: '⚡'
      },
      {
        id: 3,
        title: 'Intelligent Automation',
        description: 'Deploy adaptive AI systems that learn from your patterns.',
        icon: '🤖'
      }
    ],
    portfolio: [
      {
        id: 1,
        title: 'ARA Property Services',
        client: 'ARA Property Services',
        category: 'Property Management',
        description: 'Transformed operations through intelligent automation.'
      }
    ],
    testimonials: [
      {
        id: 1,
        author: 'John Smith',
        position: 'CEO',
        company: 'ARA Property Services',
        content: 'ALIAS transformed our business operations completely.'
      }
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech Hub, CA 94000'
    }
  };

  useEffect(() => {
    // Load content - will integrate with Basehub
    setContent(sampleContent);
  }, [currentProfileId]);

  const handleSave = async () => {
    setSaving(true);
    // Simulate save - will integrate with Basehub
    setTimeout(() => {
      setSaving(false);
      alert('Content saved successfully!');
    }, 1000);
  };

  const handleFieldEdit = (field, value) => {
    const section = activeSection;
    setContent(prev => ({
      ...prev,
      [section]: Array.isArray(prev[section]) 
        ? prev[section] 
        : { ...prev[section], [field]: value }
    }));
  };

  const renderFieldEditor = (field, value, type = 'text') => {
    const isEditing = editingField === field;
    
    if (!isEditing) {
      return (
        <div 
          className="field-value"
          onClick={() => setEditingField(field)}
        >
          {Array.isArray(value) ? value.join(', ') : value}
          <Edit3 className="edit-icon" />
        </div>
      );
    }

    return (
      <div className="field-editor">
        {type === 'textarea' ? (
          <textarea
            value={value}
            onChange={(e) => handleFieldEdit(field, e.target.value)}
            className="editor-input textarea"
            rows={4}
          />
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => handleFieldEdit(field, e.target.value)}
            className="editor-input"
          />
        )}
        <div className="editor-actions">
          <button 
            onClick={() => setEditingField(null)}
            className="btn-icon success"
          >
            <Check size={16} />
          </button>
          <button 
            onClick={() => setEditingField(null)}
            className="btn-icon cancel"
          >
            <X size={16} />
          </button>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    const sectionContent = content[activeSection];
    
    if (!sectionContent) return <div>No content available</div>;

    // Services and Portfolio sections (arrays)
    if (Array.isArray(sectionContent)) {
      return (
        <div className="array-content">
          {sectionContent.map((item, index) => (
            <div key={item.id || index} className="content-item">
              <div className="item-header">
                <h4>{item.title || item.author}</h4>
                <button className="btn-icon delete">
                  <Trash2 size={16} />
                </button>
              </div>
              {Object.entries(item).map(([key, value]) => {
                if (key === 'id') return null;
                return (
                  <div key={key} className="field-group">
                    <label>{key.charAt(0).toUpperCase() + key.slice(1)}</label>
                    {renderFieldEditor(`${activeSection}.${index}.${key}`, value)}
                  </div>
                );
              })}
            </div>
          ))}
          <button className="btn-add">
            <Plus size={16} />
            Add {activeSection.slice(0, -1)}
          </button>
        </div>
      );
    }

    // Object sections
    return (
      <div className="object-content">
        {Object.entries(sectionContent).map(([key, value]) => (
          <div key={key} className="field-group">
            <label>{key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}</label>
            {renderFieldEditor(
              key, 
              value, 
              key.includes('description') || key.includes('philosophy') || key.includes('mission') 
                ? 'textarea' 
                : 'text'
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="content-manager">
      {/* Header */}
      <header className="cm-header">
        <div className="cm-header-left">
          <Link href="/" className="back-link">
            <ArrowLeft size={20} />
            Back to Showcase
          </Link>
          <h1>Content Manager</h1>
        </div>
        <div className="cm-header-right">
          <ProfileSwitcher />
          <button 
            onClick={handleSave}
            disabled={saving}
            className="btn-save"
          >
            {saving ? (
              <>
                <RefreshCw size={16} className="spin" />
                Saving...
              </>
            ) : (
              <>
                <Save size={16} />
                Save Changes
              </>
            )}
          </button>
        </div>
      </header>

      <div className="cm-layout">
        {/* Sidebar */}
        <aside className="cm-sidebar">
          <h3>Content Sections</h3>
          <nav className="section-nav">
            {sections.map(section => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`section-btn ${activeSection === section.id ? 'active' : ''}`}
              >
                <span className="section-icon">{section.icon}</span>
                <span className="section-name">{section.name}</span>
              </button>
            ))}
          </nav>
          
          <div className="sidebar-info">
            <h4>How it works</h4>
            <p>Edit content here and it will automatically update across all showcase websites.</p>
            <p className="info-note">
              Currently viewing: <strong>{currentProfileId === 'alias' ? 'ALIAS' : 'ARA Property Services'}</strong>
            </p>
          </div>
        </aside>

        {/* Main Content */}
        <main className="cm-main">
          <div className="content-header">
            <h2>{sections.find(s => s.id === activeSection)?.name}</h2>
            <p>Click any field to edit. Changes are applied to all showcase websites.</p>
          </div>
          
          <div className="content-editor">
            {renderContent()}
          </div>
        </main>
      </div>

      <style jsx>{`
        .content-manager {
          min-height: 100vh;
          background: #0a0a0a;
          color: white;
        }

        .cm-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.5rem 2rem;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          background: rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(10px);
        }

        .cm-header-left {
          display: flex;
          align-items: center;
          gap: 2rem;
        }

        .cm-header-right {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .back-link {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #888;
          text-decoration: none;
          transition: color 0.2s;
        }

        .back-link:hover {
          color: white;
        }

        .btn-save {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background: #0066FF;
          color: white;
          border: none;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-save:hover {
          background: #0052CC;
        }

        .btn-save:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .spin {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .cm-layout {
          display: grid;
          grid-template-columns: 280px 1fr;
          min-height: calc(100vh - 80px);
        }

        .cm-sidebar {
          background: rgba(255, 255, 255, 0.03);
          border-right: 1px solid rgba(255, 255, 255, 0.1);
          padding: 2rem;
        }

        .section-nav {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-top: 1rem;
        }

        .section-btn {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          width: 100%;
          padding: 0.75rem 1rem;
          background: transparent;
          color: #888;
          border: none;
          border-radius: 0.5rem;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s;
        }

        .section-btn:hover {
          background: rgba(255, 255, 255, 0.05);
          color: white;
        }

        .section-btn.active {
          background: rgba(0, 102, 255, 0.2);
          color: #0066FF;
          border: 1px solid rgba(0, 102, 255, 0.3);
        }

        .section-icon {
          font-size: 1.25rem;
        }

        .sidebar-info {
          margin-top: 3rem;
          padding-top: 2rem;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-info h4 {
          margin-bottom: 0.5rem;
          color: #888;
          font-size: 0.875rem;
        }

        .sidebar-info p {
          font-size: 0.875rem;
          color: #666;
          line-height: 1.5;
        }

        .info-note {
          margin-top: 1rem;
          padding: 0.75rem;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 0.5rem;
        }

        .cm-main {
          padding: 2rem 3rem;
        }

        .content-header {
          margin-bottom: 2rem;
        }

        .content-header h2 {
          font-size: 2rem;
          margin-bottom: 0.5rem;
        }

        .content-header p {
          color: #888;
        }

        .field-group {
          margin-bottom: 1.5rem;
        }

        .field-group label {
          display: block;
          margin-bottom: 0.5rem;
          color: #888;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .field-value {
          padding: 0.75rem 1rem;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 0.5rem;
          cursor: pointer;
          position: relative;
          transition: all 0.2s;
        }

        .field-value:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .edit-icon {
          position: absolute;
          right: 1rem;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          opacity: 0;
          transition: opacity 0.2s;
        }

        .field-value:hover .edit-icon {
          opacity: 0.5;
        }

        .field-editor {
          display: flex;
          gap: 0.5rem;
        }

        .editor-input {
          flex: 1;
          padding: 0.75rem 1rem;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(0, 102, 255, 0.5);
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
        }

        .editor-input.textarea {
          resize: vertical;
          min-height: 100px;
        }

        .editor-actions {
          display: flex;
          gap: 0.5rem;
        }

        .btn-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          border-radius: 0.5rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-icon.success {
          background: rgba(34, 197, 94, 0.2);
          color: #22C55E;
        }

        .btn-icon.cancel {
          background: rgba(239, 68, 68, 0.2);
          color: #EF4444;
        }

        .btn-icon.delete {
          background: transparent;
          color: #EF4444;
        }

        .content-item {
          padding: 1.5rem;
          background: rgba(255, 255, 255, 0.03);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 0.5rem;
          margin-bottom: 1rem;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .btn-add {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          width: 100%;
          padding: 1rem;
          background: transparent;
          color: #0066FF;
          border: 2px dashed rgba(0, 102, 255, 0.3);
          border-radius: 0.5rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-add:hover {
          background: rgba(0, 102, 255, 0.1);
          border-color: rgba(0, 102, 255, 0.5);
        }
      `}</style>
    </div>
  );
}