// Mock BaseHub client for development
export const basehub = {
  query: async () => ({ data: null }),
};

// Fetch business profile by ID
export async function getBusinessProfile(profileId) {
  try {
    const data = await basehub.query({
      businessProfiles: {
        _filters: {
          id: { eq: profileId }
        },
        items: {
          id: true,
          name: true,
          tagline: true,
          description: true,
          logo: true,
          primaryColor: true,
          secondaryColor: true,
          active: true
        }
      }
    });
    
    return data.businessProfiles.items[0] || null;
  } catch (error) {
    console.error('Error fetching business profile:', error);
    return null;
  }
}

// Fetch content for a specific profile
export async function getProfileContent(profileId) {
  try {
    const [hero, about, services, portfolio, testimonials, contact] = await Promise.all([
      // Hero Section
      basehub.query({
        heroSections: {
          _filters: {
            profileId: { eq: profileId }
          },
          items: {
            headline: true,
            subheadline: true,
            ctaText: true,
            ctaLink: true,
            backgroundImage: true,
            backgroundVideo: true
          }
        }
      }),
      
      // About Section
      basehub.query({
        aboutSections: {
          _filters: {
            profileId: { eq: profileId }
          },
          items: {
            title: true,
            content: true,
            philosophy: true,
            mission: true,
            values: true,
            image: true
          }
        }
      }),
      
      // Services
      basehub.query({
        services: {
          _filters: {
            profileId: { eq: profileId }
          },
          _sort: {
            order: 'asc'
          },
          items: {
            title: true,
            description: true,
            icon: true,
            features: true,
            image: true
          }
        }
      }),
      
      // Portfolio Projects
      basehub.query({
        portfolioProjects: {
          _filters: {
            profileId: { eq: profileId },
            featured: { eq: true }
          },
          _sort: {
            date: 'desc'
          },
          _limit: 6,
          items: {
            title: true,
            client: true,
            category: true,
            description: true,
            thumbnail: true,
            liveUrl: true,
            tags: true
          }
        }
      }),
      
      // Testimonials
      basehub.query({
        testimonials: {
          _filters: {
            profileId: { eq: profileId }
          },
          _limit: 3,
          items: {
            author: true,
            position: true,
            company: true,
            content: true,
            avatar: true,
            rating: true
          }
        }
      }),
      
      // Contact Info
      basehub.query({
        contactInfo: {
          _filters: {
            profileId: { eq: profileId }
          },
          items: {
            email: true,
            phone: true,
            address: true,
            businessHours: true,
            socialMedia: true
          }
        }
      })
    ]);
    
    return {
      hero: hero.heroSections.items[0] || null,
      about: about.aboutSections.items[0] || null,
      services: services.services.items || [],
      portfolio: portfolio.portfolioProjects.items || [],
      testimonials: testimonials.testimonials.items || [],
      contact: contact.contactInfo.items[0] || null
    };
  } catch (error) {
    console.error('Error fetching profile content:', error);
    return {
      hero: null,
      about: null,
      services: [],
      portfolio: [],
      testimonials: [],
      contact: null
    };
  }
}

// Get current active profile from global settings
export async function getCurrentProfile() {
  try {
    const data = await basehub.query({
      globalSettings: {
        currentProfile: {
          id: true,
          name: true,
          tagline: true,
          primaryColor: true,
          secondaryColor: true
        },
        maintenanceMode: true,
        announcementBar: {
          enabled: true,
          text: true,
          link: true
        }
      }
    });
    
    return data.globalSettings;
  } catch (error) {
    console.error('Error fetching global settings:', error);
    return null;
  }
}

// Get all business profiles
export async function getAllProfiles() {
  try {
    const data = await basehub.query({
      businessProfiles: {
        _filters: {
          active: { eq: true }
        },
        items: {
          id: true,
          name: true,
          tagline: true,
          description: true,
          logo: true,
          primaryColor: true,
          secondaryColor: true
        }
      }
    });
    
    return data.businessProfiles.items || [];
  } catch (error) {
    console.error('Error fetching all profiles:', error);
    return [];
  }
}

// Get content for a specific website/profile combination
export async function getWebsiteContent(websiteId, profileId) {
  const profile = await getBusinessProfile(profileId);
  const content = await getProfileContent(profileId);
  
  // Map content to website-specific structure
  return {
    profile,
    content,
    websiteId
  };
}