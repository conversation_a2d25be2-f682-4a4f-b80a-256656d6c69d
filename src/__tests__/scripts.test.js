const { extractMetadata, scanWebsites } = require('../../scripts/collect-sites');
const { detectBuildCommand, findBuildOutput } = require('../../scripts/export-sites');
const fs = require('fs');
const path = require('path');

// Mock fs for testing
jest.mock('fs');

describe('Site Collection Scripts', () => {
  describe('extractMetadata', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('extracts metadata from valid package.json', () => {
      const mockPackageJson = {
        name: 'test-project',
        description: 'A test project',
        version: '1.0.0',
        dependencies: {
          next: '^14.0.0',
          react: '^18.0.0'
        }
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockPackageJson));

      const result = extractMetadata('/test/path');

      expect(result).toEqual({
        id: 'path',
        name: 'Test Project',
        slug: 'path',
        description: 'A test project',
        version: '1.0.0',
        framework: 'nextjs',
        port: 3000,
        path: '/test/path',
        screenshot: '/screenshots/path.png',
        hasPackageJson: true,
        scripts: {}
      });
    });

    it('returns null for missing package.json', () => {
      fs.existsSync.mockReturnValue(false);

      const result = extractMetadata('/test/path');

      expect(result).toBeNull();
    });

    it('detects different frameworks', () => {
      const testCases = [
        { deps: { vue: '^3.0.0' }, expectedFramework: 'vue', expectedPort: 8080 },
        { deps: { vite: '^4.0.0' }, expectedFramework: 'vite', expectedPort: 5173 },
        { deps: { react: '^18.0.0' }, expectedFramework: 'react', expectedPort: 3000 },
      ];

      testCases.forEach(({ deps, expectedFramework, expectedPort }) => {
        const mockPackageJson = {
          name: 'test',
          dependencies: deps
        };

        fs.existsSync.mockReturnValue(true);
        fs.readFileSync.mockReturnValue(JSON.stringify(mockPackageJson));

        const result = extractMetadata('/test/path');

        expect(result.framework).toBe(expectedFramework);
        expect(result.port).toBe(expectedPort);
      });
    });
  });

  describe('detectBuildCommand', () => {
    it('uses export script if available', () => {
      const scripts = { export: 'custom export command' };
      const result = detectBuildCommand('/test/path', 'nextjs', scripts);
      expect(result).toBe('custom export command');
    });

    it('uses build script if available', () => {
      const scripts = { build: 'custom build command' };
      const result = detectBuildCommand('/test/path', 'nextjs', scripts);
      expect(result).toBe('custom build command');
    });

    it('detects package manager from lockfiles', () => {
      fs.existsSync.mockImplementation((filePath) => {
        return filePath.includes('bun.lockb');
      });

      const result = detectBuildCommand('/test/path', 'nextjs', {});
      expect(result).toContain('bun');
    });

    it('falls back to npm for unknown package managers', () => {
      fs.existsSync.mockReturnValue(false);

      const result = detectBuildCommand('/test/path', 'nextjs', {});
      expect(result).toContain('npm');
    });
  });

  describe('findBuildOutput', () => {
    it('finds existing build directories', () => {
      fs.existsSync.mockImplementation((dirPath) => {
        return dirPath.includes('dist');
      });

      const result = findBuildOutput('/test/path', 'vite');
      expect(result).toBe('/test/path/dist');
    });

    it('returns null when no build directory exists', () => {
      fs.existsSync.mockReturnValue(false);

      const result = findBuildOutput('/test/path', 'vite');
      expect(result).toBeNull();
    });

    it('checks framework-specific directories in order', () => {
      const calls = [];
      fs.existsSync.mockImplementation((dirPath) => {
        calls.push(dirPath);
        return dirPath.includes('out'); // Only 'out' exists
      });

      const result = findBuildOutput('/test/path', 'nextjs');

      // Should check directories in order and return first existing one
      expect(calls).toContain('/test/path/out');
      expect(result).toBe('/test/path/out');
    });
  });
});
