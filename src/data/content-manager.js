// Content Management System for ALIAS Showcase
import fs from 'fs';
import path from 'path';

export const contentProfiles = {
  alias: {
    id: 'alias',
    name: 'ALIAS',
    description: 'Adaptive Learning Integrated Agentic Solutions',
    primaryColor: '#0066FF',
    secondaryColor: '#00D4FF',
    logo: '/images/alias-logo.png',
    contentFile: 'alias-content.md'
  },
  araPropertyServices: {
    id: 'ara-property-services',
    name: 'ARA Property Services',
    description: 'Professional Property Management Solutions',
    primaryColor: '#2E7D32',
    secondaryColor: '#66BB6A',
    logo: '/images/ara-logo.png',
    contentFile: 'ara-content.md'
  }
};

// Content sections that can be customized per website
export const contentSections = {
  hero: {
    headline: '',
    subheadline: '',
    cta: '',
    image: ''
  },
  about: {
    title: '',
    description: '',
    features: [],
    image: ''
  },
  services: {
    title: '',
    items: []
  },
  portfolio: {
    title: '',
    projects: []
  },
  testimonials: {
    title: '',
    items: []
  },
  contact: {
    title: '',
    email: '',
    phone: '',
    address: '',
    social: {}
  }
};

// Parse markdown content into structured data
export function parseMarkdownContent(markdown) {
  const sections = {};
  const lines = markdown.split('\n');
  let currentSection = '';
  let currentSubsection = '';
  
  lines.forEach(line => {
    if (line.startsWith('## ')) {
      currentSection = line.replace('## ', '').toLowerCase().replace(/\s+/g, '-');
      sections[currentSection] = {};
    } else if (line.startsWith('### ')) {
      currentSubsection = line.replace('### ', '').toLowerCase().replace(/\s+/g, '-');
      sections[currentSection][currentSubsection] = '';
    } else if (currentSection && currentSubsection && line.trim()) {
      sections[currentSection][currentSubsection] += line + '\n';
    }
  });
  
  return sections;
}

// Website-specific content mapping
export const websiteContentMap = {
  'format-archive': {
    sections: ['hero', 'services', 'portfolio', 'contact'],
    customizations: {
      layout: 'ecommerce',
      features: ['cart', 'catalog', 'checkout']
    }
  },
  'isochrome': {
    sections: ['hero', 'about', 'services', 'portfolio', 'contact'],
    customizations: {
      layout: 'agency',
      features: ['portfolio', 'team', 'process']
    }
  },
  'algora': {
    sections: ['hero', 'about', 'services', 'contact'],
    customizations: {
      layout: 'showcase',
      features: ['timeline', 'case-studies', 'gallery']
    }
  },
  'wu-wei': {
    sections: ['hero', 'about', 'portfolio', 'contact'],
    customizations: {
      layout: 'creative',
      features: ['animations', 'interactive', 'experimental']
    }
  },
  'nico-palmer': {
    sections: ['hero', 'about', 'portfolio', 'testimonials', 'contact'],
    customizations: {
      layout: 'portfolio',
      features: ['projects', 'reviews', 'faq']
    }
  },
  'otis-valen': {
    sections: ['hero', 'portfolio', 'about', 'contact'],
    customizations: {
      layout: 'artistic',
      features: ['gallery', 'exhibitions', 'bio']
    }
  },
  'alex-finley': {
    sections: ['hero', 'services', 'portfolio', 'about', 'contact'],
    customizations: {
      layout: 'photography',
      features: ['gallery', 'services', 'booking']
    }
  }
};

// Load content for a specific profile
export async function loadContent(profileId = 'alias') {
  const profile = contentProfiles[profileId];
  if (!profile) return null;
  
  try {
    const contentPath = path.join(process.cwd(), 'src/data', profile.contentFile);
    const markdown = await fs.promises.readFile(contentPath, 'utf-8');
    return {
      profile,
      content: parseMarkdownContent(markdown)
    };
  } catch (error) {
    console.error('Error loading content:', error);
    return null;
  }
}

// Get content for a specific website
export function getWebsiteContent(websiteId, profileId = 'alias') {
  const websiteConfig = websiteContentMap[websiteId];
  const content = loadContent(profileId);
  
  if (!websiteConfig || !content) return null;
  
  return {
    ...content,
    sections: websiteConfig.sections,
    customizations: websiteConfig.customizations
  };
}