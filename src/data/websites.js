export const websites = [
  // Monthly Release Projects (CGMWT Series)
  {
    id: "format-archive",
    name: "NEXUS·COMMERCE",
    tagline: "Digital Marketplace",
    description: "Enterprise-grade commerce platform showcasing AI-powered product discovery and intelligent automation",
    category: "E-Commerce",
    path: "CGMWTAPR2025/Source Code/format-archive",
    port: 6661,
    tech: ["Next.js 15.3.1", "React 19", "GSAP", "Zustand"],
    features: ["AI Product Matching", "Smart Cart", "Predictive Analytics", "Real-time Sync"],
    status: "running"
  },
  {
    id: "isochrome",
    name: "PRISM·CREATIVE",
    tagline: "Design Innovation Hub",
    description: "Creative studio website with smooth scrolling and modern design patterns",
    category: "Agency",
    path: "CGMWTFEB2025/Source Code/isochrome",
    port: 6662,
    tech: ["Next.js 15.1.7", "React 18", "GSAP", "Lenis"],
    features: ["3D Visualization", "Data Art", "Motion Design", "Interactive Demos"],
    status: "running"
  },
  {
    id: "algora",
    name: "QUANTUM·TECH",
    tagline: "Future Tech Showcase",
    description: "Demonstrating tomorrow's enterprise solutions with Lottie animations and advanced interactions",
    category: "Technology",
    path: "CGMWTJAN2025/Source Code/algora",
    port: 6663,
    tech: ["Next.js 15.1.5", "React 18", "GSAP", "Lottie"],
    features: ["AI Experiments", "Quantum Demos", "Neural Networks", "Edge Computing"],
    status: "running"
  },
  {
    id: "wu-wei",
    name: "FLUX·LAB",
    tagline: "Innovation Playground", 
    description: "Minimalist design project showcasing experimental web technologies",
    category: "Experimental",
    path: "CGMWTJUNE2025/Source Code/wu-wei",
    port: 6664,
    tech: ["Next.js", "React", "GSAP"],
    features: ["WebGL Experiments", "Particle Systems", "Generative Design", "AI Prototypes"],
    status: "running"
  },
  {
    id: "nico-palmer",
    name: "ECHO·CONSULTING",
    tagline: "Strategic Solutions",
    description: "Portfolio site built with Vite and React 19, featuring Framer Motion animations",
    category: "Portfolio",
    path: "CGMWTMAR2025/Source Code/nico-palmer",
    port: 6665,
    tech: ["Vite", "React 19", "GSAP", "Framer Motion"],
    features: ["Case Studies", "ROI Calculator", "Client Portal", "Strategy Tools"],
    status: "running"
  },
  {
    id: "otis-valen",
    name: "AURORA·GALLERY",
    tagline: "Digital Exhibitions",
    description: "Multi-page portfolio with GSAP animations built on Vite and vanilla JavaScript",
    category: "Portfolio",
    path: "CGMWTMAY2025/Source Code/otis-valen",
    port: 6666,
    tech: ["Vite", "Vanilla JS", "GSAP", "Lenis"],
    features: ["3D Gallery", "VR Tours", "Interactive Stories", "Data Sculptures"],
    status: "running"
  },
  {
    id: "alex-finley",
    name: "VERTEX·STUDIO",
    tagline: "Digital Craftsmanship",
    description: "Photographer portfolio website with vanilla HTML/CSS/JS and GSAP animations",
    category: "Portfolio",
    path: "CGMWTOCT2024/Source Code/alex-finley",
    port: 6667,
    tech: ["Static HTML", "GSAP", "Lenis", "SplitType"],
    features: ["Image Galleries", "Smooth Transitions", "Custom Animations", "Portfolio Showcase"],
    status: "running"
  },

  // Next.js Component Demos
  {
    id: "bitkraft-menu-nextjs",
    name: "BITKRAFT·MENU",
    tagline: "Animated Navigation",
    description: "Next.js animated menu component with sound effects and GSAP transitions",
    category: "Components",
    path: "cg-bitkraft-menu-nextjs",
    port: 6668,
    tech: ["Next.js 15.3.3", "React 19", "GSAP"],
    features: ["Sound Effects", "Smooth Animations", "Responsive Design", "Reusable Component"],
    status: "running"
  },
  {
    id: "capsules-animated-columns-nextjs",
    name: "CAPSULES·COLUMNS",
    tagline: "Layout Animation",
    description: "Next.js project featuring animated column layouts with smooth transitions",
    category: "Components",
    path: "cg-capsules-animated-columns-nextjs",
    port: 6669,
    tech: ["Next.js", "React", "GSAP"],
    features: ["Column Animations", "Layout Transitions", "Grid Systems", "Performance Optimized"],
    status: "running"
  },
  {
    id: "interactive-particle-logo-nextjs",
    name: "PARTICLE·LOGO",
    tagline: "Interactive Graphics",
    description: "Next.js interactive particle effects for logo animations and brand experiences",
    category: "Components",
    path: "cg-interactive-particle-logo-nextjs",
    port: 6670,
    tech: ["Next.js", "React", "Canvas API"],
    features: ["Particle Effects", "Interactive Graphics", "Brand Animation", "WebGL"],
    status: "running"
  },
  {
    id: "warp-slider-next",
    name: "WARP·SLIDER",
    tagline: "Custom Slider",
    description: "Next.js custom shader-based slider with advanced visual effects",
    category: "Components",
    path: "cg-warp-slider-next",
    port: 6671,
    tech: ["Next.js", "WebGL", "Shaders"],
    features: ["Custom Shaders", "Warp Effects", "Slider Component", "Visual Innovation"],
    status: "running"
  },
  {
    id: "direction-aware-hover-nextjs",
    name: "DIRECTION·AWARE",
    tagline: "Hover Effects",
    description: "Next.js direction-aware hover animations for enhanced user interactions",
    category: "Components",
    path: "codegrid-direction-aware-hover-effect-nextjs",
    port: 6672,
    tech: ["Next.js", "React", "GSAP"],
    features: ["Direction Detection", "Hover Effects", "Smooth Animations", "User Experience"],
    status: "running"
  },
  {
    id: "text-reveal-animation-nextjs",
    name: "TEXT·REVEAL",
    tagline: "Text Animation",
    description: "Next.js text reveal animations using modern animation techniques",
    category: "Components",
    path: "codegrid-nextjs-text-reveal-animation",
    port: 6673,
    tech: ["Next.js", "React", "GSAP", "SplitType"],
    features: ["Text Animations", "Reveal Effects", "Typography", "Split Text"],
    status: "running"
  },
  {
    id: "zajno-page-transitions-nextjs",
    name: "PAGE·TRANSITIONS",
    tagline: "Route Animations",
    description: "Next.js page transition effects inspired by Zajno design studio",
    category: "Components",
    path: "codegrid-zajno-page-transitions-nextjs",
    port: 6674,
    tech: ["Next.js", "React", "GSAP"],
    features: ["Page Transitions", "Route Animations", "Smooth Navigation", "Modern Design"],
    status: "running"
  },

  // Vanilla JavaScript Demos
  {
    id: "bitkraft-menu-vanilla",
    name: "BITKRAFT·MENU·JS",
    tagline: "Vanilla Navigation",
    description: "Vanilla JavaScript animated menu with sound effects and smooth transitions",
    category: "Vanilla JS",
    path: "cg-bitkraft-menu",
    port: 6675,
    tech: ["HTML", "CSS", "Vanilla JS", "GSAP"],
    features: ["Sound Effects", "Pure JavaScript", "No Dependencies", "Performance"],
    status: "running"
  },
  {
    id: "sticky-cards-javascript",
    name: "STICKY·CARDS",
    tagline: "Scroll Animation",
    description: "Vite + Vanilla JavaScript sticky cards with smooth scrolling effects",
    category: "Vanilla JS",
    path: "cg-capsules-sticky-cards-javascript",
    port: 6676,
    tech: ["Vite", "Vanilla JS", "GSAP", "Lenis"],
    features: ["Sticky Scroll", "Card Animations", "Smooth Scrolling", "Vite Build"],
    status: "running"
  },
  {
    id: "eduardbodak-scroll-animation",
    name: "SCROLL·MASTER",
    tagline: "Scroll Effects",
    description: "Vanilla JavaScript scroll animations inspired by Eduard Bodak's design",
    category: "Vanilla JS",
    path: "cg-eduardbodak-scroll-animation",
    port: 6677,
    tech: ["HTML", "CSS", "Vanilla JS"],
    features: ["Scroll Triggers", "Parallax Effects", "Performance Optimized", "Responsive"],
    status: "running"
  },
  {
    id: "p10-landing-page-reveal",
    name: "LANDING·REVEAL",
    tagline: "Page Reveals",
    description: "Vite + GSAP landing page with sophisticated reveal animations",
    category: "Vanilla JS",
    path: "cg-p10-landing-page-reveal-gsap",
    port: 6678,
    tech: ["Vite", "GSAP", "Vanilla JS"],
    features: ["Landing Page", "Reveal Animations", "GSAP Timeline", "Modern Build"],
    status: "running"
  },
  {
    id: "horizontal-scroll-animation",
    name: "HORIZONTAL·SCROLL",
    tagline: "Horizontal Effects",
    description: "Vanilla JavaScript horizontal scroll animation with smooth transitions",
    category: "Vanilla JS",
    path: "cg-radga-horizontal-scroll-animation",
    port: 6679,
    tech: ["HTML", "CSS", "Vanilla JS"],
    features: ["Horizontal Scroll", "Smooth Transitions", "Performance", "Responsive Design"],
    status: "running"
  },
  {
    id: "sofihealth-product-scroll",
    name: "PRODUCT·3D",
    tagline: "3D Product Animation",
    description: "3D product scroll animation with advanced visual effects",
    category: "Vanilla JS",
    path: "cg-sofihealth-product-scroll-animation",
    port: 6680,
    tech: ["HTML", "CSS", "Vanilla JS", "3D CSS"],
    features: ["3D Animations", "Product Showcase", "Scroll Integration", "Visual Effects"],
    status: "running"
  },
  {
    id: "direction-aware-hover-vanilla",
    name: "DIRECTION·AWARE·JS",
    tagline: "Vanilla Hover",
    description: "Pure JavaScript direction-aware hover effects without frameworks",
    category: "Vanilla JS",
    path: "codegrid-direction-aware-hover-effect-javascript",
    port: 6681,
    tech: ["HTML", "CSS", "Vanilla JS"],
    features: ["Direction Detection", "Pure JavaScript", "Lightweight", "No Dependencies"],
    status: "running"
  },

  // Special Projects
  {
    id: "combat-mirror-pitch",
    name: "COMBAT·MIRROR",
    tagline: "3D Presentation",
    description: "Next.js 3D presentation with Three.js, React Three Fiber, and Recharts",
    category: "3D Projects",
    path: "combat-mirror-pitch",
    port: 6682,
    tech: ["Next.js 15.1.5", "React 19", "Three.js", "React Three Fiber", "Recharts"],
    features: ["3D Graphics", "Data Visualization", "Interactive Presentation", "WebGL"],
    status: "running"
  },
  {
    id: "claudia-desktop",
    name: "CLAUDIA·DESKTOP",
    tagline: "Desktop Application",
    description: "Tauri + React + TypeScript desktop application with Rust backend",
    category: "Desktop Apps",
    path: "../claudia",
    port: 6683,
    tech: ["Tauri", "React", "TypeScript", "Rust", "Radix UI", "Tailwind CSS"],
    features: ["Desktop App", "Rust Backend", "Cross Platform", "Native Performance"],
    status: "development"
  }
];

export const categories = [
  { name: "All", count: websites.length, icon: "⚡" },
  { name: "E-Commerce", count: websites.filter(w => w.category === "E-Commerce").length, icon: "🛒" },
  { name: "Agency", count: websites.filter(w => w.category === "Agency").length, icon: "🎨" },
  { name: "Technology", count: websites.filter(w => w.category === "Technology").length, icon: "🚀" },
  { name: "Portfolio", count: websites.filter(w => w.category === "Portfolio").length, icon: "👤" },
  { name: "Components", count: websites.filter(w => w.category === "Components").length, icon: "🧩" },
  { name: "Vanilla JS", count: websites.filter(w => w.category === "Vanilla JS").length, icon: "⚡" },
  { name: "3D Projects", count: websites.filter(w => w.category === "3D Projects").length, icon: "🎮" },
  { name: "Desktop Apps", count: websites.filter(w => w.category === "Desktop Apps").length, icon: "💻" },
  { name: "Experimental", count: websites.filter(w => w.category === "Experimental").length, icon: "🧪" }
];