{"generated": "2025-07-07T01:59:37.952Z", "count": 14, "sites": [{"id": "cg-bitkraft-menu", "name": "Bitkraft Menu", "slug": "cg-bitkraft-menu", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-bitkraft-menu", "screenshot": "/screenshots/cg-bitkraft-menu.png", "hasPackageJson": true, "scripts": {"dev": "vite"}}, {"id": "cg-bitkraft-menu-nextjs", "name": "Bitkraft Menu Nextjs", "slug": "cg-bitkraft-menu-nextjs", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-bitkraft-menu-nextjs", "screenshot": "/screenshots/cg-bitkraft-menu-nextjs.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "cg-capsules-animated-columns-nextjs", "name": "Capsules Animated Columns Nextjs", "slug": "cg-capsules-animated-columns-nextjs", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-capsules-animated-columns-nextjs", "screenshot": "/screenshots/cg-capsules-animated-columns-nextjs.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "cg-capsules-sticky-cards-javascript", "name": "Capsules Sticky Cards Javascript", "slug": "cg-capsules-sticky-cards-javascript", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-capsules-sticky-cards-javascript", "screenshot": "/screenshots/cg-capsules-sticky-cards-javascript.png", "hasPackageJson": true, "scripts": {"dev": "vite", "build": "vite build"}}, {"id": "combat-mirror-pitch", "name": "Combat Mirror Pitch", "slug": "combat-mirror-pitch", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/combat-mirror-pitch", "screenshot": "/screenshots/combat-mirror-pitch.png", "hasPackageJson": true, "scripts": {"dev": "basehub dev & next dev", "build": "basehub && next build", "start": "next start", "lint": "next lint"}}, {"id": "codegrid-direction-aware-hover-effect-javascript", "name": "Direction Aware Hover Effect Javascript", "slug": "codegrid-direction-aware-hover-effect-javascript", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/codegrid-direction-aware-hover-effect-javascript", "screenshot": "/screenshots/codegrid-direction-aware-hover-effect-javascript.png", "hasPackageJson": true, "scripts": {"dev": "vite", "build": "vite build"}}, {"id": "codegrid-direction-aware-hover-effect-nextjs", "name": "Direction Aware Hover Effect Nextjs", "slug": "codegrid-direction-aware-hover-effect-nextjs", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/codegrid-direction-aware-hover-effect-nextjs", "screenshot": "/screenshots/codegrid-direction-aware-hover-effect-nextjs.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "cg-eduardbodak-scroll-animation", "name": "Eduardbodak Scroll Animation", "slug": "cg-eduardbodak-scroll-animation", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-eduardbodak-scroll-animation", "screenshot": "/screenshots/cg-eduardbodak-scroll-animation.png", "hasPackageJson": true, "scripts": {"dev": "vite"}}, {"id": "cg-interactive-particle-logo-nextjs", "name": "Interactive Particle Logo Nextjs", "slug": "cg-interactive-particle-logo-nextjs", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-interactive-particle-logo-nextjs", "screenshot": "/screenshots/cg-interactive-particle-logo-nextjs.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "codegrid-nextjs-text-reveal-animation", "name": "Nextjs Text Reveal Animation", "slug": "codegrid-nextjs-text-reveal-animation", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/codegrid-nextjs-text-reveal-animation", "screenshot": "/screenshots/codegrid-nextjs-text-reveal-animation.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "cg-p10-landing-page-reveal-gsap", "name": "P10 Landing Page Reveal Gsap", "slug": "cg-p10-landing-page-reveal-gsap", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-p10-landing-page-reveal-gsap", "screenshot": "/screenshots/cg-p10-landing-page-reveal-gsap.png", "hasPackageJson": true, "scripts": {"dev": "vite"}}, {"id": "cg-sofihealth-product-scroll-animation", "name": "Sofihealth Product Scroll Animation", "slug": "cg-sofihealth-product-scroll-animation", "description": "No description available", "version": "1.0.0", "framework": "vite", "port": 5173, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-sofihealth-product-scroll-animation", "screenshot": "/screenshots/cg-sofihealth-product-scroll-animation.png", "hasPackageJson": true, "scripts": {"dev": "vite"}}, {"id": "cg-warp-slider-next", "name": "Warp Slider Next", "slug": "cg-warp-slider-next", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/cg-warp-slider-next", "screenshot": "/screenshots/cg-warp-slider-next.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}, {"id": "codegrid-zajno-page-transitions-nextjs", "name": "Zajno Page Transitions Nextjs", "slug": "codegrid-zajno-page-transitions-nextjs", "description": "No description available", "version": "0.1.0", "framework": "nextjs", "port": 3000, "path": "/Users/<USER>/Downloads/CodeGRID/Websites/codegrid-zajno-page-transitions-nextjs", "screenshot": "/screenshots/codegrid-zajno-page-transitions-nextjs.png", "hasPackageJson": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}}]}