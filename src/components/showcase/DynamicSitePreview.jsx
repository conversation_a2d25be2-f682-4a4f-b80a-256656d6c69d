"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

export default function DynamicSitePreview({ website }) {
  const [status, setStatus] = useState('idle'); // idle, starting, running, error
  const [siteUrl, setSiteUrl] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkSiteStatus();
  }, [website.id]);

  const checkSiteStatus = async () => {
    try {
      const res = await fetch(`/api/sites/${website.id}`);
      const data = await res.json();
      
      if (data.isRunning) {
        setStatus('running');
        setSiteUrl(data.url);
      }
    } catch (error) {
      console.error('Failed to check site status:', error);
    }
  };

  const startSite = async () => {
    setStatus('starting');
    
    try {
      const res = await fetch(`/api/sites/${website.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'start' })
      });
      
      const data = await res.json();
      
      if (res.ok) {
        setSiteUrl(data.url);
        setStatus('running');
        setError(null);
      } else {
        setStatus('error');
        setError(data.error || 'Failed to start site');
        console.error('Failed to start site:', data);
      }
    } catch (error) {
      setStatus('error');
      setError('Network error: ' + error.message);
      console.error('Failed to start site:', error);
    }
  };

  const stopSite = async () => {
    try {
      await fetch(`/api/sites/${website.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' })
      });
      
      setStatus('idle');
      setSiteUrl(null);
    } catch (error) {
      console.error('Failed to stop site:', error);
    }
  };

  return (
    <div className="site-preview-card">
      <div className="site-header">
        <div className="site-info">
          <h3>{website.name}</h3>
          <p className="site-tagline">{website.tagline}</p>
          <div className="site-meta">
            <span className="site-category">{website.category}</span>
            <span className="site-port">Port {website.port}</span>
          </div>
        </div>
        
        <div className="site-status">
          {status === 'idle' && (
            <span className="status-badge idle">Not Running</span>
          )}
          {status === 'starting' && (
            <span className="status-badge starting">Starting...</span>
          )}
          {status === 'running' && (
            <span className="status-badge running">Running</span>
          )}
          {status === 'error' && (
            <span className="status-badge error">Error</span>
          )}
        </div>
      </div>

      <div className="site-preview">
        {status === 'running' ? (
          <iframe
            src={siteUrl}
            className="preview-iframe"
            title={website.name}
            onError={() => setStatus('error')}
          />
        ) : (
          <div className="preview-placeholder">
            <div className="placeholder-content">
              <h4>{website.name}</h4>
              <p>{website.description}</p>
              <div className="tech-stack">
                {website.tech.map((tech, idx) => (
                  <span key={idx} className="tech-badge">{tech}</span>
                ))}
              </div>
              {status === 'idle' && (
                <button 
                  onClick={startSite}
                  className="btn btn-primary start-btn"
                >
                  Start Preview
                </button>
              )}
              {status === 'starting' && (
                <div className="loading-spinner">
                  <div className="spinner"></div>
                  <p>Starting server...</p>
                </div>
              )}
              {status === 'error' && (
                <div className="error-message">
                  <p className="error-title">Failed to start</p>
                  <p className="error-detail">{error}</p>
                  <button 
                    onClick={() => {
                      setStatus('idle');
                      setError(null);
                    }}
                    className="btn btn-secondary retry-btn"
                  >
                    Try Again
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="site-actions">
        {status === 'running' ? (
          <>
            <Link 
              href={`/preview/${website.id}`}
              className="btn btn-secondary"
            >
              Device Preview
            </Link>
            <a 
              href={siteUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary"
            >
              Open Full Site
            </a>
            <button 
              onClick={stopSite}
              className="btn btn-danger"
            >
              Stop
            </button>
          </>
        ) : (
          <button 
            onClick={startSite}
            className="btn btn-primary"
            disabled={status === 'starting'}
          >
            {status === 'starting' ? 'Starting...' : 'Start Site'}
          </button>
        )}
      </div>

      <style jsx>{`
        .site-preview-card {
          background: var(--color-surface);
          border: 1px solid var(--color-border);
          border-radius: 0.75rem;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .site-preview-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .site-header {
          padding: 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          border-bottom: 1px solid var(--color-border);
        }

        .site-info h3 {
          font-size: 1.25rem;
          margin: 0 0 0.5rem 0;
        }

        .site-tagline {
          color: var(--color-text-secondary);
          font-size: 0.875rem;
          margin: 0 0 0.75rem 0;
        }

        .site-meta {
          display: flex;
          gap: 1rem;
          font-size: 0.75rem;
        }

        .site-category {
          color: var(--color-primary);
          font-weight: 600;
        }

        .site-port {
          color: var(--color-text-secondary);
        }

        .status-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .status-badge.idle {
          background: rgba(255, 255, 255, 0.1);
          color: var(--color-text-secondary);
        }

        .status-badge.starting {
          background: rgba(255, 200, 0, 0.2);
          color: #ffc800;
        }

        .status-badge.running {
          background: rgba(0, 255, 100, 0.2);
          color: #00ff64;
        }

        .status-badge.error {
          background: rgba(255, 0, 0, 0.2);
          color: #ff0000;
        }

        .site-preview {
          height: 400px;
          position: relative;
          background: #000;
        }

        .preview-iframe {
          width: 100%;
          height: 100%;
          border: none;
        }

        .preview-placeholder {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
        }

        .placeholder-content {
          text-align: center;
          padding: 2rem;
        }

        .placeholder-content h4 {
          font-size: 1.5rem;
          margin: 0 0 1rem 0;
        }

        .placeholder-content p {
          color: var(--color-text-secondary);
          max-width: 400px;
          margin: 0 auto 1.5rem;
        }

        .tech-stack {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          justify-content: center;
          margin-bottom: 1.5rem;
        }

        .tech-badge {
          background: rgba(255, 255, 255, 0.1);
          padding: 0.25rem 0.75rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
        }

        .loading-spinner {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
        }

        .spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(255, 255, 255, 0.1);
          border-top-color: var(--color-primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .site-actions {
          padding: 1rem 1.5rem;
          display: flex;
          gap: 0.75rem;
          background: var(--color-surface-light);
          border-top: 1px solid var(--color-border);
        }

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 0.25rem;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.3s ease;
          text-decoration: none;
          display: inline-block;
        }

        .btn-primary {
          background: var(--color-primary);
          color: white;
        }

        .btn-secondary {
          background: var(--color-surface);
          color: var(--color-foreground);
          border: 1px solid var(--color-border);
        }

        .btn-danger {
          background: rgba(255, 0, 0, 0.2);
          color: #ff0000;
          border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .start-btn {
          margin-top: 1rem;
        }

        .error-message {
          text-align: center;
          padding: 1rem;
          background: rgba(255, 0, 0, 0.1);
          border-radius: 0.5rem;
          margin-top: 1rem;
        }

        .error-title {
          color: #ff0000;
          font-weight: 600;
          margin: 0 0 0.5rem 0;
        }

        .error-detail {
          color: var(--color-text-secondary);
          font-size: 0.875rem;
          margin: 0 0 1rem 0;
        }

        .retry-btn {
          font-size: 0.875rem;
        }
      `}</style>
    </div>
  );
}