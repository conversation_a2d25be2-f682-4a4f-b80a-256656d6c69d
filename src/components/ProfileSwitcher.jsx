'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAllProfiles, useProfileSwitcher } from '../hooks/useBasehub.js';
import { ChevronDown } from 'lucide-react';

export default function ProfileSwitcher() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const { profiles, loading } = useAllProfiles();
  const { currentProfileId, switchProfile } = useProfileSwitcher();
  
  // For now, use static data until Basehub is configured
  const staticProfiles = [
    {
      id: 'alias',
      name: 'ALIAS',
      tagline: 'Adaptive Learning Integrated Agentic Solutions',
      primaryColor: '#0066FF',
      secondaryColor: '#00D4FF'
    },
    {
      id: 'ara-property-services',
      name: 'ARA Property Services',
      tagline: 'Professional Property Management Solutions',
      primaryColor: '#2E7D32',
      secondaryColor: '#66BB6A'
    }
  ];
  
  const availableProfiles = profiles.length > 0 ? profiles : staticProfiles;
  const currentProfile = availableProfiles.find(p => p.id === currentProfileId) || availableProfiles[0];
  
  const handleProfileChange = (profileId) => {
    switchProfile(profileId);
    setIsOpen(false);
    // Refresh the router to apply new profile
    router.refresh();
  };
  
  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg hover:bg-white/20 transition-colors"
        style={{ borderColor: currentProfile.primaryColor }}
      >
        <div className="text-left">
          <div className="text-sm font-medium text-white">{currentProfile.name}</div>
          <div className="text-xs text-white/70">{currentProfile.tagline}</div>
        </div>
        <ChevronDown className={`w-4 h-4 text-white transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>
      
      {isOpen && (
        <div className="absolute top-full mt-2 right-0 w-80 bg-black/90 backdrop-blur-xl rounded-lg shadow-2xl border border-white/10 overflow-hidden z-50">
          <div className="p-2">
            {availableProfiles.map((profile) => (
              <button
                key={profile.id}
                onClick={() => handleProfileChange(profile.id)}
                className={`w-full text-left p-3 rounded-lg transition-all hover:bg-white/10 ${
                  profile.id === currentProfileId ? 'bg-white/10' : ''
                }`}
              >
                <div 
                  className="flex items-center gap-3"
                  style={{ 
                    borderLeft: `3px solid ${profile.primaryColor}`,
                    paddingLeft: '12px'
                  }}
                >
                  <div>
                    <div className="font-medium text-white">{profile.name}</div>
                    <div className="text-sm text-white/70">{profile.tagline}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
          
          <div className="border-t border-white/10 p-3 bg-white/5">
            <p className="text-xs text-white/50 text-center">
              Switch between business profiles to preview different content
            </p>
          </div>
        </div>
      )}
    </div>
  );
}