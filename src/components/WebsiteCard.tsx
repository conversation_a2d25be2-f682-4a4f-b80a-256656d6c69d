"use client";

import { useState, memo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, CardContent, Badge, Button } from '../ui';

interface WebsiteCardProps {
  website: {
    id: string;
    name: string;
    description: string;
    category: string;
    port: number;
    status: string;
    tech: string[];
    features?: string[];
    tagline?: string;
    screenshot?: string;
  };
  priority?: boolean;
}

const WebsiteCard = memo(function WebsiteCard({ website, priority = false }: WebsiteCardProps) {
  const [hoveredCard, setHoveredCard] = useState(false);
  const [imageError, setImageError] = useState(false);

  return (
    <Card
      variant="elevated"
      className="website-card group transition-all duration-300 hover:scale-105 hover:shadow-xl"
      onMouseEnter={() => setHoveredCard(true)}
      onMouseLeave={() => setHoveredCard(false)}
    >
      <div className="relative overflow-hidden rounded-t-lg">
        {/* Screenshot or placeholder */}
        <div className="aspect-video bg-gradient-to-br from-blue-50 to-indigo-100 relative">
          {website.screenshot && !imageError ? (
            <Image
              src={website.screenshot}
              alt={`${website.name} screenshot`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-110"
              priority={priority}
              onError={() => setImageError(true)}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg">
                <span className="text-2xl font-bold text-gray-600">
                  {website.name.charAt(0)}
                </span>
              </div>
            </div>
          )}
          
          {/* Status indicator */}
          <div className="absolute top-3 right-3">
            <Badge 
              variant={website.status === 'active' ? 'success' : 'warning'}
              size="sm"
            >
              Port {website.port}
            </Badge>
          </div>
        </div>
      </div>

      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {website.name}
            </h3>
            {website.tagline && (
              <p className="text-sm text-blue-600 font-medium mt-1">
                {website.tagline}
              </p>
            )}
          </div>

          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
            {website.description}
          </p>

          {/* Features */}
          {website.features && website.features.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {website.features.slice(0, 2).map((feature) => (
                <Badge key={feature} variant="secondary" size="sm">
                  {feature}
                </Badge>
              ))}
              {website.features.length > 2 && (
                <Badge variant="default" size="sm">
                  +{website.features.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Tech stack */}
          <div className="flex flex-wrap gap-1">
            {website.tech.slice(0, 3).map((tech) => (
              <span
                key={tech}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md font-medium"
              >
                {tech}
              </span>
            ))}
            {website.tech.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-md">
                +{website.tech.length - 3}
              </span>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="primary"
              size="sm"
              className="flex-1"
              onClick={() => window.open(`http://localhost:${website.port}`, '_blank')}
            >
              View Live
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              asChild
            >
              <Link href={`/preview/${website.id}`}>
                Preview
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

export { WebsiteCard };
