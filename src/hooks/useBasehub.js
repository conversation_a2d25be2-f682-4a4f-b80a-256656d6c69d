'use client';

import { useState, useEffect } from 'react';
import {
  getBusinessProfile,
  getProfileContent,
  getCurrentProfile,
  getAllProfiles,
  getWebsiteContent
} from '../lib/basehub-client.js';

// Hook to fetch and manage current profile
export function useCurrentProfile() {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProfile() {
      try {
        const settings = await getCurrentProfile();
        if (settings?.currentProfile) {
          setProfile(settings.currentProfile);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchProfile();
  }, []);

  return { profile, loading, error };
}

// Hook to fetch all profiles
export function useAllProfiles() {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProfiles() {
      try {
        const data = await getAllProfiles();
        setProfiles(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchProfiles();
  }, []);

  return { profiles, loading, error };
}

// Hook to fetch profile content
export function useProfileContent(profileId) {
  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!profileId) {
      setLoading(false);
      return;
    }

    async function fetchContent() {
      try {
        const data = await getProfileContent(profileId);
        setContent(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchContent();
  }, [profileId]);

  return { content, loading, error };
}

// Hook to fetch website-specific content
export function useWebsiteContent(websiteId, profileId) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!websiteId || !profileId) {
      setLoading(false);
      return;
    }

    async function fetchData() {
      try {
        const result = await getWebsiteContent(websiteId, profileId);
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [websiteId, profileId]);

  return { data, loading, error };
}

// Hook to switch between profiles
export function useProfileSwitcher() {
  const [currentProfileId, setCurrentProfileId] = useState('alias');
  
  const switchProfile = (profileId) => {
    setCurrentProfileId(profileId);
    // Store in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedProfile', profileId);
    }
  };

  // Load saved profile on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('selectedProfile');
      if (saved) {
        setCurrentProfileId(saved);
      }
    }
  }, []);

  return { currentProfileId, switchProfile };
}