# ALIAS Showcase

A comprehensive showcase system for demonstrating web solutions with dynamic content management and business profile switching.

## Overview

ALIAS Showcase allows you to:
- Display multiple website templates and components
- Switch between different business profiles (ALIAS, client aliases)
- Manage content centrally through Basehub CMS
- Preview sites on different devices
- Run all showcase sites on sequential ports (6661-6667)

## Features

### 🎯 Multi-Profile System
- **ALIAS** - Main company profile
- **ARA Property Services** - Example client alias
- Easy switching between profiles
- Content dynamically updates across all sites

### 🎨 Showcase Websites
1. **Format Archive** (Port 6661) - E-commerce template
2. **Isochrome** (Port 6662) - Creative agency
3. **Algora** (Port 6663) - Tech showcase
4. **Wu-Wei** (Port 6664) - Interactive portfolio
5. **<PERSON>** (Port 6665) - Photography portfolio
6. **<PERSON>** (Port 6666) - Art gallery
7. **<PERSON>** (Port 6667) - Creative studio

### 📝 Content Management
- Visual content editor at `/content-manager`
- Basehub CMS integration
- Edit once, update everywhere
- Support for all content types (text, images, lists)

### 📱 Device Previews
- iPhone 16 Pro Max mockup
- iPad Pro 12.9" mockup
- Desktop view
- Responsive testing

## Getting Started

### Prerequisites
- Node.js 18+
- Bun package manager
- Basehub account (for CMS features)

### Installation

1. Clone the repository
```bash
cd /Users/<USER>/Downloads/CodeGRID/Websites/codegrid-showcase
```

2. Install dependencies
```bash
bun install
```

3. Set up environment variables
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Basehub credentials:
```env
BASEHUB_TOKEN=your_basehub_token
NEXT_PUBLIC_BASEHUB_URL=https://api.basehub.com/your-repo
```

4. Start the showcase
```bash
bun dev
```

5. Start all showcase websites
```bash
cd ..
./start-simple.sh
```

## Usage

### Viewing the Showcase
1. Open http://localhost:3000
2. Browse available websites
3. Click "View Live" to see running sites
4. Click "Preview" for device mockups

### Switching Profiles
1. Use the profile switcher in the top-right
2. Select between ALIAS or ARA Property Services
3. All content updates automatically

### Managing Content
1. Navigate to http://localhost:3000/content-manager
2. Select a content section from the sidebar
3. Click any field to edit
4. Save changes to sync with Basehub

### Adding New Websites
1. Add website to `src/data/websites.js`
2. Assign next sequential port (6668, 6669, etc.)
3. Update `start-simple.sh` script
4. Map content sections in `src/data/content-manager.js`

## Architecture

### Technology Stack
- **Next.js 15.3.4** - React framework
- **Basehub** - Headless CMS
- **GSAP** - Animations
- **Three.js** - 3D graphics
- **Tailwind CSS** - Styling

### Project Structure
```
codegrid-showcase/
├── app/                    # Next.js app router
│   ├── page.js            # Main showcase page
│   ├── content-manager/   # Content management UI
│   └── preview/[id]/      # Device preview pages
├── src/
│   ├── components/        # React components
│   ├── data/             # Content and configuration
│   ├── hooks/            # Custom React hooks
│   └── lib/              # Utilities and API clients
├── public/               # Static assets
└── .basehub/            # Basehub type definitions
```

## Scripts

### Development
```bash
bun dev              # Start showcase in dev mode
bun build           # Build for production
bun start           # Start production server
```

### Website Management
```bash
./start-simple.sh    # Start all showcase websites
./stop-all.sh       # Stop all running websites
```

## Basehub Setup

See [BASEHUB_SETUP.md](./BASEHUB_SETUP.md) for detailed instructions on:
- Creating your Basehub account
- Configuring collections
- Importing initial content
- Managing multiple profiles

## The ALIAS Concept

ALIAS (Adaptive Learning Integrated Agentic Solutions) creates "digital twins" of businesses:
- Complete digital representation of business operations
- Enables risk-free testing and optimization
- Allows deep understanding before transformation
- Creates "alias" versions for client demonstrations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a pull request

## License

© 2024 ALIAS - Adaptive Learning Integrated Agentic Solutions. All rights reserved.