# CodeGRID Showcase

A modern Next.js-based showcase system for ALIAS (Adaptive Learning Integrated Agentic Solutions) that demonstrates various web solutions with dynamic content management, automated site collection, and performance optimization.

## 🚀 Features

- **Automated Site Discovery**: Automatically scans and collects sibling websites
- **Multi-Profile Business System**: Switch between different business profiles (ALIAS, ARA Property Services)
- **Static Site Generation**: Pre-renders all pages for optimal performance
- **Performance Optimized**: ISR, Edge Runtime, lazy loading, and bundle optimization
- **Modern Architecture**: Next.js 15 App Router with route groups and TypeScript
- **Design System**: Centralized UI components with Tailwind CSS
- **Testing & CI/CD**: Comprehensive testing suite with GitHub Actions
- **Content Management**: Powered by Basehub CMS for flexible content updates

## 🛠 Tech Stack

- **Framework**: Next.js 15.3.4 with App Router and Turbopack
- **Runtime**: React 19.0.0 with concurrent features
- **Language**: TypeScript 5+ with strict type checking
- **Styling**: Tailwind CSS v4 with custom design tokens
- **Package Manager**: Bun for fast dependency management
- **Build Tool**: Turbo for optimized monorepo builds
- **CMS**: Basehub for headless content management
- **3D Graphics**: React Three Fiber + Three.js
- **Animations**: GSAP for smooth interactions
- **Testing**: Jest + React Testing Library
- **Linting**: ESLint 9 with TypeScript support
- **CI/CD**: GitHub Actions with performance monitoring

## �� Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd codegrid-showcase
```

2. **Install dependencies**:
```bash
bun install
```

3. **Set up environment variables**:
```bash
cp .env.example .env.local
# Add your Basehub API keys and configuration
```

4. **Collect sibling websites**:
```bash
bun run collect-sites
```

5. **Run the development server**:
```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) to view the showcase.

## 🏗 Architecture

### Modern Next.js App Router Structure

```
app/
├── (showcase)/           # Route group for main showcase
│   ├── layout.tsx       # Showcase-specific layout
│   ├── page.tsx         # Main gallery page
│   ├── [slug]/          # Dynamic website pages
│   ├── preview/         # Website preview system
│   ├── content-manager/ # CMS interface
│   └── not-found.tsx    # Custom 404 page
├── api/                 # API routes
├── globals.css          # Global styles
└── layout.js           # Root layout
```

### Performance Optimizations

- **Static Generation**: All pages pre-rendered at build time
- **ISR (Incremental Static Regeneration)**: Content updates every 60 seconds
- **Edge Runtime**: API routes run on Vercel Edge Network
- **Lazy Loading**: Components loaded on demand with `next/dynamic`
- **Image Optimization**: Next.js Image component with WebP/AVIF
- **Bundle Analysis**: Automated bundle size monitoring

### Site Collection System

The showcase automatically discovers and manages websites through:

1. **Discovery**: Scans `../../` for directories with `package.json`
2. **Metadata Extraction**: Analyzes dependencies to determine framework
3. **Port Assignment**: Sequential allocation starting from 6661
4. **Static Export**: Builds and exports sites for iframe embedding
5. **Screenshot Generation**: Captures preview images for gallery

## 🎨 Design System

Centralized UI components in `src/ui/`:

- **Button**: Multiple variants (primary, secondary, outline, ghost)
- **Card**: Flexible container with header, content, footer
- **Badge**: Status indicators and labels
- **Typography**: Consistent text styles
- **Colors**: Brand-aligned color palette
- **Animations**: Smooth transitions and micro-interactions

## �� Testing & Quality

### Testing Framework
- **Unit Tests**: Jest + React Testing Library
- **Component Tests**: UI component testing
- **Integration Tests**: Script and utility testing
- **Coverage**: 70% minimum threshold

### Code Quality
- **ESLint**: Modern flat config with TypeScript rules
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for quality gates
- **TypeScript**: Strict type checking

### Performance Monitoring
- **Lighthouse CI**: Automated performance audits
- **Bundle Analysis**: Size tracking and optimization
- **Web Vitals**: Core performance metrics
- **Security Scanning**: Vulnerability detection

## 📜 Available Scripts

### Development
- `bun dev` - Start development server with Turbopack
- `bun build` - Production build with optimizations
- `bun start` - Start production server
- `bun export` - Generate static export

### Site Management
- `bun run collect-sites` - Scan and collect sibling websites
- `bun run export-sites` - Build and export all sites
- `bun run export-siblings` - Full collection and export pipeline

### Quality Assurance
- `bun run lint` - ESLint with auto-fix
- `bun run typecheck` - TypeScript type checking
- `bun run format` - Prettier code formatting
- `bun run test` - Run test suite
- `bun run test:watch` - Watch mode testing
- `bun run test:coverage` - Coverage report

### Analysis
- `bun run analyze` - Bundle size analysis
- `bun run lighthouse` - Performance audit

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Environment Variables
```env
# Basehub CMS
BASEHUB_TOKEN=your_basehub_token
BASEHUB_REF=main

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

### CI/CD Pipeline
- **Automated Testing**: Runs on every PR
- **Bundle Analysis**: Size impact reporting
- **Security Scanning**: Vulnerability detection
- **Performance Monitoring**: Lighthouse audits
- **Preview Deployments**: Automatic PR previews
- **Production Deployment**: Auto-deploy on main branch

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes following the coding standards
4. Run quality checks: `bun run lint && bun run typecheck && bun run test`
5. Commit with conventional commits: `feat: add amazing feature`
6. Push and create a pull request

### Coding Standards
- **TypeScript**: Strict mode with proper typing
- **Components**: Functional components with hooks
- **Styling**: Tailwind CSS with design system tokens
- **Testing**: Test all new components and utilities
- **Documentation**: Update README for significant changes

### Adding New Websites
1. Create project in sibling directory with `package.json`
2. Run `bun run collect-sites` to discover
3. Ensure proper build scripts for static export
4. Test preview functionality

## 📊 Performance Targets

- **Lighthouse Performance**: > 80
- **First Contentful Paint**: < 2s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Total Blocking Time**: < 300ms

## 🔧 Troubleshooting

### Common Issues

**Sites not appearing in gallery**:
- Ensure `package.json` exists in sibling directories
- Run `bun run collect-sites` to refresh
- Check console for scanning errors

**Build failures**:
- Verify all dependencies are installed: `bun install`
- Check TypeScript errors: `bun run typecheck`
- Review build logs for specific errors

**Performance issues**:
- Run bundle analysis: `bun run analyze`
- Check for large dependencies or unused code
- Verify image optimization settings

## 📄 License

This project is part of the ALIAS Innovation Lab ecosystem. All rights reserved.
