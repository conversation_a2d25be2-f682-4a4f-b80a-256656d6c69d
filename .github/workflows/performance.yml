name: Performance Monitoring

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  schedule:
    # Run performance tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  lighthouse:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build application
        run: bun run build

      - name: Start application
        run: |
          bun run start &
          sleep 10
        env:
          NODE_ENV: production

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v12
        with:
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

  bundle-size:
    name: Bundle Size Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Analyze bundle size
        uses: preactjs/compressed-size-action@v2
        with:
          repo-token: '${{ secrets.GITHUB_TOKEN }}'
          pattern: '.next/static/**/*.{js,css}'
          exclude: '{**/*.map,**/node_modules/**}'
          minimum-change-threshold: 100

  web-vitals:
    name: Web Vitals Monitoring
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build application
        run: bun run build

      - name: Start application
        run: |
          bun run start &
          sleep 10
        env:
          NODE_ENV: production

      - name: Run Web Vitals tests
        run: |
          npx @web/test-runner \
            --playwright \
            --browsers chromium \
            --config web-vitals.config.js

      - name: Upload Web Vitals results
        uses: actions/upload-artifact@v4
        with:
          name: web-vitals-results
          path: web-vitals-results.json
          retention-days: 30
