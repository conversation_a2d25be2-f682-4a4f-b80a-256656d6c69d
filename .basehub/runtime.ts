// This file is automatically generated by Basehub
// Do not edit manually

export interface BaseHubDocument {
  _id: string;
  _createdAt: string;
  _updatedAt: string;
  _slug?: string;
}

export interface BusinessProfile extends BaseHubDocument {
  id: string;
  name: string;
  tagline?: string;
  description?: any;
  logo?: { url: string; alt?: string };
  primaryColor: string;
  secondaryColor: string;
  active: boolean;
}

export interface HeroSection extends BaseHubDocument {
  profileId: string;
  headline: string;
  subheadline?: string;
  ctaText: string;
  ctaLink?: string;
  backgroundImage?: { url: string; alt?: string };
  backgroundVideo?: string;
}

export interface AboutSection extends BaseHubDocument {
  profileId: string;
  title: string;
  content?: any;
  philosophy?: any;
  mission?: any;
  values?: string[];
  image?: { url: string; alt?: string };
}

export interface Service extends BaseHubDocument {
  profileId: string;
  title: string;
  description?: any;
  icon?: string;
  features?: string[];
  image?: { url: string; alt?: string };
  order: number;
}

export interface PortfolioProject extends BaseHubDocument {
  profileId: string;
  title: string;
  client?: string;
  category?: string;
  description?: any;
  thumbnail?: { url: string; alt?: string };
  images?: Array<{ url: string; alt?: string }>;
  videoUrl?: string;
  liveUrl?: string;
  tags?: string[];
  featured: boolean;
  date?: string;
}

export interface Testimonial extends BaseHubDocument {
  profileId: string;
  author: string;
  position?: string;
  company?: string;
  content?: any;
  avatar?: { url: string; alt?: string };
  rating?: number;
}

export interface TeamMember extends BaseHubDocument {
  profileId: string;
  name: string;
  position?: string;
  bio?: any;
  photo?: { url: string; alt?: string };
  social?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
    email?: string;
  };
}

export interface ContactInfo extends BaseHubDocument {
  profileId: string;
  email?: string;
  phone?: string;
  address?: any;
  businessHours?: any;
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };
}

export interface GlobalSettings extends BaseHubDocument {
  currentProfile?: BusinessProfile;
  maintenanceMode: boolean;
  announcementBar?: {
    enabled: boolean;
    text?: string;
    link?: string;
  };
}